[{"C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx": "4", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx": "5", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx": "6", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx": "7", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx": "8", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx": "9", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx": "10", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx": "11", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx": "12", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx": "13", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx": "14", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx": "15", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx": "16", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx": "17", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx": "18", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx": "19", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx": "20", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx": "21", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx": "22", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx": "23", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx": "24", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx": "25", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx": "26", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx": "27", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx": "28", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx": "29", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx": "30", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx": "31", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx": "32", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx": "33", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx": "34", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx": "35", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx": "36", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx": "37", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx": "38", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx": "39", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx": "40", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx": "41", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx": "42", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx": "43", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx": "44", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx": "45", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx": "46", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx": "47", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx": "48", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx": "49", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx": "50", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx": "51", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx": "52", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx": "53", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx": "54", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx": "55", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx": "56", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx": "57", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx": "58", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx": "59", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx": "60", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx": "61", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx": "62", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx": "63", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx": "64", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx": "65", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx": "66", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx": "67", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx": "68", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx": "69", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx": "70", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx": "71", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx": "72", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx": "73", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx": "74", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx": "75", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx": "76", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx": "77", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js": "78", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js": "79", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js": "80", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx": "81", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js": "82", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx": "83", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx": "84", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx": "85", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx": "86", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx": "87", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx": "88", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx": "89", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx": "90", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx": "91", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx": "92", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx": "93", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx": "94", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js": "95", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx": "96", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx": "97", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx": "98", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx": "99", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx": "100", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx": "101", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx": "102", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx": "103", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx": "104", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx": "105", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx": "106", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx": "107", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js": "108", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx": "109", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx": "110", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx": "111", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx": "112", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx": "113", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx": "114", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js": "115", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx": "116", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx": "117", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx": "118", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx": "119", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx": "120", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx": "121", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx": "122", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx": "123", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx": "124", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx": "125", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx": "126", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx": "127", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx": "128", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx": "129", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx": "130", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx": "131", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx": "132", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx": "133", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx": "134", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx": "135", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js": "136", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx": "137", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx": "138", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx": "139", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx": "140", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx": "141", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx": "142", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx": "143", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx": "144", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx": "145", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx": "146", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx": "147", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js": "148", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx": "149", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx": "150", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx": "151", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx": "152", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx": "153", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js": "154", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx": "155", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx": "156", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx": "157", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx": "158", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\logoUtils.js": "159", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayUPayment.jsx": "160", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\CancelPayUPayment.jsx": "161", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetailsWithPayu.jsx": "162", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx": "163", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\public\\PublicCourseDetails.jsx": "164", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\feed.jsx": "165", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\Feed.jsx": "166", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\FeedPost.jsx": "167", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\feedPost.jsx": "168", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\MyFeed.jsx": "169", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\feedServices.js": "170", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\public\\PublicPostDetails.jsx": "171"}, {"size": 552, "mtime": 1750490066000, "results": "172", "hashOfConfig": "173"}, {"size": 375, "mtime": 1750490066000, "results": "174", "hashOfConfig": "173"}, {"size": 2236, "mtime": 1754146017116, "results": "175", "hashOfConfig": "173"}, {"size": 7498, "mtime": 1753966524806, "results": "176", "hashOfConfig": "173"}, {"size": 4772, "mtime": 1754042012879, "results": "177", "hashOfConfig": "173"}, {"size": 1597, "mtime": 1753966888856, "results": "178", "hashOfConfig": "173"}, {"size": 2212, "mtime": 1751054548000, "results": "179", "hashOfConfig": "173"}, {"size": 2266, "mtime": 1751054540000, "results": "180", "hashOfConfig": "173"}, {"size": 8556, "mtime": 1753951754639, "results": "181", "hashOfConfig": "173"}, {"size": 5573, "mtime": 1753383910530, "results": "182", "hashOfConfig": "173"}, {"size": 14993, "mtime": 1753383833576, "results": "183", "hashOfConfig": "173"}, {"size": 5376, "mtime": 1753383939567, "results": "184", "hashOfConfig": "173"}, {"size": 4637, "mtime": 1753383886944, "results": "185", "hashOfConfig": "173"}, {"size": 9424, "mtime": 1753383999422, "results": "186", "hashOfConfig": "173"}, {"size": 882, "mtime": 1750498842000, "results": "187", "hashOfConfig": "173"}, {"size": 856, "mtime": 1753967137577, "results": "188", "hashOfConfig": "173"}, {"size": 23107, "mtime": 1753190819653, "results": "189", "hashOfConfig": "173"}, {"size": 64676, "mtime": 1753170429987, "results": "190", "hashOfConfig": "173"}, {"size": 53481, "mtime": 1752484548147, "results": "191", "hashOfConfig": "173"}, {"size": 65216, "mtime": 1752490834024, "results": "192", "hashOfConfig": "173"}, {"size": 4643, "mtime": 1752409801600, "results": "193", "hashOfConfig": "173"}, {"size": 4216, "mtime": 1752409801605, "results": "194", "hashOfConfig": "173"}, {"size": 40073, "mtime": 1752409801612, "results": "195", "hashOfConfig": "173"}, {"size": 36784, "mtime": 1752409801613, "results": "196", "hashOfConfig": "173"}, {"size": 51452, "mtime": 1753276336843, "results": "197", "hashOfConfig": "173"}, {"size": 12687, "mtime": 1751710504000, "results": "198", "hashOfConfig": "173"}, {"size": 169, "mtime": 1752677915041, "results": "199", "hashOfConfig": "173"}, {"size": 22397, "mtime": 1753988124765, "results": "200", "hashOfConfig": "173"}, {"size": 77456, "mtime": 1752409801619, "results": "201", "hashOfConfig": "173"}, {"size": 22490, "mtime": 1752409801616, "results": "202", "hashOfConfig": "173"}, {"size": 26511, "mtime": 1752409801617, "results": "203", "hashOfConfig": "173"}, {"size": 5517, "mtime": 1752409801623, "results": "204", "hashOfConfig": "173"}, {"size": 32410, "mtime": 1752409801620, "results": "205", "hashOfConfig": "173"}, {"size": 44282, "mtime": 1753903422856, "results": "206", "hashOfConfig": "173"}, {"size": 100330, "mtime": 1752409801626, "results": "207", "hashOfConfig": "173"}, {"size": 7690, "mtime": 1751768538000, "results": "208", "hashOfConfig": "173"}, {"size": 15453, "mtime": 1751722792000, "results": "209", "hashOfConfig": "173"}, {"size": 54929, "mtime": 1752409801636, "results": "210", "hashOfConfig": "173"}, {"size": 36510, "mtime": 1752920830735, "results": "211", "hashOfConfig": "173"}, {"size": 3661, "mtime": 1752338196000, "results": "212", "hashOfConfig": "173"}, {"size": 54017, "mtime": 1752829580976, "results": "213", "hashOfConfig": "173"}, {"size": 11659, "mtime": 1752411851791, "results": "214", "hashOfConfig": "173"}, {"size": 1670, "mtime": 1752409801634, "results": "215", "hashOfConfig": "173"}, {"size": 5099, "mtime": 1752836652921, "results": "216", "hashOfConfig": "173"}, {"size": 10347, "mtime": 1753280697739, "results": "217", "hashOfConfig": "173"}, {"size": 5709, "mtime": 1752409801648, "results": "218", "hashOfConfig": "173"}, {"size": 48841, "mtime": 1752409801651, "results": "219", "hashOfConfig": "173"}, {"size": 11615, "mtime": 1752097860000, "results": "220", "hashOfConfig": "173"}, {"size": 27087, "mtime": 1752409801621, "results": "221", "hashOfConfig": "173"}, {"size": 9781, "mtime": 1753216567180, "results": "222", "hashOfConfig": "173"}, {"size": 1734, "mtime": 1751059658000, "results": "223", "hashOfConfig": "173"}, {"size": 2266, "mtime": 1753216737273, "results": "224", "hashOfConfig": "173"}, {"size": 8117, "mtime": 1752409801668, "results": "225", "hashOfConfig": "173"}, {"size": 10141, "mtime": 1753961432373, "results": "226", "hashOfConfig": "173"}, {"size": 27402, "mtime": 1753911674606, "results": "227", "hashOfConfig": "173"}, {"size": 4076, "mtime": 1751035930000, "results": "228", "hashOfConfig": "173"}, {"size": 4662, "mtime": 1752409801676, "results": "229", "hashOfConfig": "173"}, {"size": 4567, "mtime": 1753879568970, "results": "230", "hashOfConfig": "173"}, {"size": 40890, "mtime": 1752409801601, "results": "231", "hashOfConfig": "173"}, {"size": 49748, "mtime": 1752409801602, "results": "232", "hashOfConfig": "173"}, {"size": 29513, "mtime": 1752409801607, "results": "233", "hashOfConfig": "173"}, {"size": 20882, "mtime": 1752409801608, "results": "234", "hashOfConfig": "173"}, {"size": 7771, "mtime": 1751767656000, "results": "235", "hashOfConfig": "173"}, {"size": 7751, "mtime": 1751767830000, "results": "236", "hashOfConfig": "173"}, {"size": 8583, "mtime": 1751710504000, "results": "237", "hashOfConfig": "173"}, {"size": 9357, "mtime": 1753007074236, "results": "238", "hashOfConfig": "173"}, {"size": 9025, "mtime": 1750975280000, "results": "239", "hashOfConfig": "173"}, {"size": 7922, "mtime": 1750490066000, "results": "240", "hashOfConfig": "173"}, {"size": 23471, "mtime": 1752409801655, "results": "241", "hashOfConfig": "173"}, {"size": 5224, "mtime": 1750548386000, "results": "242", "hashOfConfig": "173"}, {"size": 22425, "mtime": 1752677199172, "results": "243", "hashOfConfig": "173"}, {"size": 4066, "mtime": 1750548608000, "results": "244", "hashOfConfig": "173"}, {"size": 3862, "mtime": 1750490066000, "results": "245", "hashOfConfig": "173"}, {"size": 5099, "mtime": 1752836952067, "results": "246", "hashOfConfig": "173"}, {"size": 10840, "mtime": 1754151322505, "results": "247", "hashOfConfig": "173"}, {"size": 14135, "mtime": 1753359213497, "results": "248", "hashOfConfig": "173"}, {"size": 10347, "mtime": 1753280688735, "results": "249", "hashOfConfig": "173"}, {"size": 13237, "mtime": 1750509658000, "results": "250", "hashOfConfig": "173"}, {"size": 760, "mtime": 1750582154000, "results": "251", "hashOfConfig": "173"}, {"size": 1464, "mtime": 1753081064033, "results": "252", "hashOfConfig": "173"}, {"size": 61762, "mtime": 1752931709150, "results": "253", "hashOfConfig": "173"}, {"size": 28115, "mtime": 1753957307161, "results": "254", "hashOfConfig": "173"}, {"size": 8756, "mtime": 1753280085442, "results": "255", "hashOfConfig": "173"}, {"size": 0, "mtime": 1751054256000, "results": "256", "hashOfConfig": "173"}, {"size": 21900, "mtime": 1752409801578, "results": "257", "hashOfConfig": "173"}, {"size": 409, "mtime": 1750490066000, "results": "258", "hashOfConfig": "173"}, {"size": 10020, "mtime": 1753861868097, "results": "259", "hashOfConfig": "173"}, {"size": 8024, "mtime": 1753280076928, "results": "260", "hashOfConfig": "173"}, {"size": 11878, "mtime": 1753384103074, "results": "261", "hashOfConfig": "173"}, {"size": 13611, "mtime": 1754157568943, "results": "262", "hashOfConfig": "173"}, {"size": 488, "mtime": 1751579418000, "results": "263", "hashOfConfig": "173"}, {"size": 506, "mtime": 1751579400000, "results": "264", "hashOfConfig": "173"}, {"size": 1452, "mtime": 1751437768000, "results": "265", "hashOfConfig": "173"}, {"size": 23613, "mtime": 1752409801630, "results": "266", "hashOfConfig": "173"}, {"size": 10859, "mtime": 1753957288586, "results": "267", "hashOfConfig": "173"}, {"size": 25665, "mtime": 1752409801627, "results": "268", "hashOfConfig": "173"}, {"size": 19281, "mtime": 1752409801594, "results": "269", "hashOfConfig": "173"}, {"size": 16387, "mtime": 1752409801614, "results": "270", "hashOfConfig": "173"}, {"size": 30692, "mtime": 1752409801629, "results": "271", "hashOfConfig": "173"}, {"size": 20468, "mtime": 1752409801591, "results": "272", "hashOfConfig": "173"}, {"size": 23434, "mtime": 1752409801593, "results": "273", "hashOfConfig": "173"}, {"size": 3508, "mtime": 1752049504000, "results": "274", "hashOfConfig": "173"}, {"size": 2614, "mtime": 1751826792000, "results": "275", "hashOfConfig": "173"}, {"size": 1923, "mtime": 1751826502000, "results": "276", "hashOfConfig": "173"}, {"size": 50190, "mtime": 1752409801633, "results": "277", "hashOfConfig": "173"}, {"size": 1927, "mtime": 1751826456000, "results": "278", "hashOfConfig": "173"}, {"size": 23878, "mtime": 1752409801643, "results": "279", "hashOfConfig": "173"}, {"size": 1708, "mtime": 1752408694807, "results": "280", "hashOfConfig": "173"}, {"size": 7643, "mtime": 1750763974000, "results": "281", "hashOfConfig": "173"}, {"size": 23256, "mtime": 1752409801645, "results": "282", "hashOfConfig": "173"}, {"size": 21217, "mtime": 1752409801647, "results": "283", "hashOfConfig": "173"}, {"size": 22643, "mtime": 1752409801644, "results": "284", "hashOfConfig": "173"}, {"size": 2997, "mtime": 1751882506000, "results": "285", "hashOfConfig": "173"}, {"size": 16290, "mtime": 1752409801649, "results": "286", "hashOfConfig": "173"}, {"size": 388, "mtime": 1751774832000, "results": "287", "hashOfConfig": "173"}, {"size": 9439, "mtime": 1751882686000, "results": "288", "hashOfConfig": "173"}, {"size": 8837, "mtime": 1751887002000, "results": "289", "hashOfConfig": "173"}, {"size": 1896, "mtime": 1751609914000, "results": "290", "hashOfConfig": "173"}, {"size": 4484, "mtime": 1751882686000, "results": "291", "hashOfConfig": "173"}, {"size": 4450, "mtime": 1751482138000, "results": "292", "hashOfConfig": "173"}, {"size": 16839, "mtime": 1752683212096, "results": "293", "hashOfConfig": "173"}, {"size": 5743, "mtime": 1750625032000, "results": "294", "hashOfConfig": "173"}, {"size": 5240, "mtime": 1752054098000, "results": "295", "hashOfConfig": "173"}, {"size": 12656, "mtime": 1750906260000, "results": "296", "hashOfConfig": "173"}, {"size": 49712, "mtime": 1751053448000, "results": "297", "hashOfConfig": "173"}, {"size": 7643, "mtime": 1750763974000, "results": "298", "hashOfConfig": "173"}, {"size": 13763, "mtime": 1753981157055, "results": "299", "hashOfConfig": "173"}, {"size": 18218, "mtime": 1752409801667, "results": "300", "hashOfConfig": "173"}, {"size": 9495, "mtime": 1752435437340, "results": "301", "hashOfConfig": "173"}, {"size": 11333, "mtime": 1753981175463, "results": "302", "hashOfConfig": "173"}, {"size": 13304, "mtime": 1752435292539, "results": "303", "hashOfConfig": "173"}, {"size": 5931, "mtime": 1750543146000, "results": "304", "hashOfConfig": "173"}, {"size": 430, "mtime": 1750591742000, "results": "305", "hashOfConfig": "173"}, {"size": 11350, "mtime": 1751037418000, "results": "306", "hashOfConfig": "173"}, {"size": 5807, "mtime": 1750533816000, "results": "307", "hashOfConfig": "173"}, {"size": 8092, "mtime": 1754163328801, "results": "308", "hashOfConfig": "173"}, {"size": 3831, "mtime": 1750490066000, "results": "309", "hashOfConfig": "173"}, {"size": 8425, "mtime": 1751813544000, "results": "310", "hashOfConfig": "173"}, {"size": 1406, "mtime": 1750548574000, "results": "311", "hashOfConfig": "173"}, {"size": 18082, "mtime": 1753957327201, "results": "312", "hashOfConfig": "173"}, {"size": 10944, "mtime": 1752484370321, "results": "313", "hashOfConfig": "173"}, {"size": 19638, "mtime": 1753276143523, "results": "314", "hashOfConfig": "173"}, {"size": 17989, "mtime": 1752697222607, "results": "315", "hashOfConfig": "173"}, {"size": 8503, "mtime": 1752680006516, "results": "316", "hashOfConfig": "173"}, {"size": 7673, "mtime": 1752680102177, "results": "317", "hashOfConfig": "173"}, {"size": 626, "mtime": 1751536590000, "results": "318", "hashOfConfig": "173"}, {"size": 18515, "mtime": 1752683186304, "results": "319", "hashOfConfig": "173"}, {"size": 1686, "mtime": 1752409801587, "results": "320", "hashOfConfig": "173"}, {"size": 2272, "mtime": 1752409801638, "results": "321", "hashOfConfig": "173"}, {"size": 145, "mtime": 1753959723348, "results": "322", "hashOfConfig": "173"}, {"size": 52979, "mtime": 1752685608016, "results": "323", "hashOfConfig": "173"}, {"size": 26870, "mtime": 1752827191316, "results": "324", "hashOfConfig": "173"}, {"size": 16901, "mtime": 1752409801670, "results": "325", "hashOfConfig": "173"}, {"size": 925, "mtime": 1752409801586, "results": "326", "hashOfConfig": "173"}, {"size": 52070, "mtime": 1752697034820, "results": "327", "hashOfConfig": "173"}, {"size": 184, "mtime": 1752749250332, "results": "328", "hashOfConfig": "173"}, {"size": 6226, "mtime": 1753383969771, "results": "329", "hashOfConfig": "173"}, {"size": 8705, "mtime": 1753278652965, "results": "330", "hashOfConfig": "173"}, {"size": 1258, "mtime": 1753386028029, "results": "331", "hashOfConfig": "173"}, {"size": 7621, "mtime": 1753885536281, "results": "332", "hashOfConfig": "173"}, {"size": 5672, "mtime": 1753885538738, "results": "333", "hashOfConfig": "173"}, {"size": 11695, "mtime": 1753890036950, "results": "334", "hashOfConfig": "173"}, {"size": 11352, "mtime": 1753890023205, "results": "335", "hashOfConfig": "173"}, {"size": 28791, "mtime": 1754163328801, "results": "336", "hashOfConfig": "173"}, {"size": 11686, "mtime": 1754033288366, "results": "337", "hashOfConfig": "173"}, {"size": 30718, "mtime": 1754162541033, "results": "338", "hashOfConfig": "173"}, {"size": 8378, "mtime": 1754163908459, "results": "339", "hashOfConfig": "173"}, {"size": 3897, "mtime": 1754033205290, "results": "340", "hashOfConfig": "173"}, {"size": 28632, "mtime": 1754162637198, "results": "341", "hashOfConfig": "173"}, {"size": 2875, "mtime": 1754150638938, "results": "342", "hashOfConfig": "173"}, {"size": 10567, "mtime": 1754163043235, "results": "343", "hashOfConfig": "173"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h3wgbg", {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js", ["857"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx", ["858"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx", ["859"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx", ["860"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx", ["861", "862"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx", ["863", "864"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx", ["865"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx", ["866"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx", ["867", "868"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx", ["869", "870", "871", "872"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx", ["873", "874", "875", "876"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx", ["877", "878", "879"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx", ["880", "881"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx", ["882", "883", "884"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx", ["885", "886"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx", ["887", "888", "889", "890"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx", ["891"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx", ["892"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx", ["893", "894", "895", "896", "897", "898"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx", ["899", "900", "901"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx", ["902", "903", "904", "905"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx", ["906"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx", ["907", "908", "909", "910"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx", ["911"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx", ["912", "913"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx", ["914", "915"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx", ["916", "917", "918", "919", "920", "921"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx", ["922"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx", ["923", "924"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx", ["925"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx", ["926", "927", "928"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx", ["929", "930", "931", "932", "933"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx", ["934", "935"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx", ["936", "937", "938"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx", ["939"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx", ["940"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx", ["941", "942", "943", "944", "945"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx", ["946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx", ["963", "964", "965"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx", ["966", "967"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx", ["968"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx", ["969", "970", "971"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx", ["972", "973", "974"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx", ["975", "976", "977"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx", ["978"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx", ["979"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx", ["980", "981"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx", ["982", "983"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx", ["984", "985"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx", ["986"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx", ["987", "988"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx", ["989"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx", ["990"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx", ["991", "992", "993"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx", ["994", "995", "996"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx", ["997", "998", "999", "1000", "1001", "1002"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx", ["1003", "1004"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx", ["1005"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx", ["1006", "1007"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx", ["1008", "1009"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx", ["1010", "1011"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx", ["1012"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx", ["1013", "1014"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx", ["1015"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx", ["1016"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx", ["1017", "1018", "1019", "1020", "1021", "1022", "1023"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx", ["1024"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx", ["1025"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx", ["1026"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx", ["1027"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx", ["1028"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx", ["1029"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx", ["1030", "1031", "1032", "1033"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx", ["1034"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx", ["1035"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx", ["1036", "1037", "1038", "1039", "1040", "1041", "1042"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx", ["1043", "1044"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx", ["1045", "1046"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx", ["1047"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx", ["1048", "1049"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx", ["1050", "1051", "1052", "1053"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx", ["1054"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx", ["1055", "1056"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx", ["1057", "1058"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx", ["1059"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx", ["1060", "1061"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx", ["1062", "1063", "1064", "1065"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx", ["1066", "1067"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx", ["1068"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx", ["1069", "1070"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx", ["1071", "1072", "1073", "1074"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx", ["1075", "1076", "1077", "1078", "1079"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx", ["1080"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx", ["1081", "1082"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx", ["1083", "1084", "1085", "1086"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx", ["1087"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx", ["1088"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\logoUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayUPayment.jsx", ["1089"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\CancelPayUPayment.jsx", ["1090", "1091"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetailsWithPayu.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx", ["1092", "1093"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\public\\PublicCourseDetails.jsx", ["1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\feed.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\Feed.jsx", ["1116", "1117", "1118", "1119", "1120"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\FeedPost.jsx", ["1121", "1122"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\feedPost.jsx", ["1123", "1124"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\feed\\MyFeed.jsx", ["1125", "1126", "1127", "1128"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\feedServices.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\public\\PublicPostDetails.jsx", ["1129"], [], {"ruleId": "1130", "severity": 1, "message": "1131", "line": 1, "column": 40, "nodeType": "1132", "messageId": "1133", "endLine": 1, "endColumn": 48}, {"ruleId": "1130", "severity": 1, "message": "1134", "line": 3, "column": 17, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1134", "line": 1, "column": 17, "nodeType": "1132", "messageId": "1133", "endLine": 1, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1135", "line": 16, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 16, "endColumn": 27}, {"ruleId": "1130", "severity": 1, "message": "1136", "line": 2, "column": 18, "nodeType": "1132", "messageId": "1133", "endLine": 2, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1137", "line": 5, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1136", "line": 2, "column": 18, "nodeType": "1132", "messageId": "1133", "endLine": 2, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1137", "line": 5, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1138", "line": 7, "column": 20, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 30}, {"ruleId": "1130", "severity": 1, "message": "1139", "line": 14, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 14, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1140", "line": 34, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 34, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1141", "line": 34, "column": 23, "nodeType": "1132", "messageId": "1133", "endLine": 34, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1142", "line": 24, "column": 25, "nodeType": "1132", "messageId": "1133", "endLine": 24, "endColumn": 41}, {"ruleId": "1130", "severity": 1, "message": "1143", "line": 26, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 26, "endColumn": 23}, {"ruleId": "1144", "severity": 1, "message": "1145", "line": 221, "column": 6, "nodeType": "1146", "endLine": 221, "endColumn": 8, "suggestions": "1147"}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 229, "column": 5, "nodeType": "1150", "messageId": "1151", "endLine": 260, "endColumn": 6}, {"ruleId": "1130", "severity": 1, "message": "1152", "line": 10, "column": 22, "nodeType": "1132", "messageId": "1133", "endLine": 10, "endColumn": 32}, {"ruleId": "1130", "severity": 1, "message": "1153", "line": 49, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 49, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1154", "line": 50, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 50, "endColumn": 24}, {"ruleId": "1144", "severity": 1, "message": "1155", "line": 110, "column": 8, "nodeType": "1146", "endLine": 110, "endColumn": 48, "suggestions": "1156"}, {"ruleId": "1144", "severity": 1, "message": "1157", "line": 94, "column": 6, "nodeType": "1146", "endLine": 94, "endColumn": 26, "suggestions": "1158"}, {"ruleId": "1130", "severity": 1, "message": "1159", "line": 237, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 237, "endColumn": 31}, {"ruleId": "1130", "severity": 1, "message": "1160", "line": 474, "column": 17, "nodeType": "1132", "messageId": "1133", "endLine": 474, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1161", "line": 212, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 212, "endColumn": 27}, {"ruleId": "1130", "severity": 1, "message": "1162", "line": 229, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 229, "endColumn": 28}, {"ruleId": "1130", "severity": 1, "message": "1163", "line": 13, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 19}, {"ruleId": "1144", "severity": 1, "message": "1164", "line": 131, "column": 8, "nodeType": "1146", "endLine": 131, "endColumn": 67, "suggestions": "1165"}, {"ruleId": "1144", "severity": 1, "message": "1166", "line": 142, "column": 8, "nodeType": "1146", "endLine": 142, "endColumn": 39, "suggestions": "1167"}, {"ruleId": "1130", "severity": 1, "message": "1168", "line": 17, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 17, "endColumn": 17}, {"ruleId": "1144", "severity": 1, "message": "1169", "line": 60, "column": 8, "nodeType": "1146", "endLine": 60, "endColumn": 10, "suggestions": "1170"}, {"ruleId": "1144", "severity": 1, "message": "1171", "line": 79, "column": 6, "nodeType": "1146", "endLine": 79, "endColumn": 82, "suggestions": "1172"}, {"ruleId": "1130", "severity": 1, "message": "1173", "line": 256, "column": 13, "nodeType": "1132", "messageId": "1133", "endLine": 256, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1173", "line": 278, "column": 13, "nodeType": "1132", "messageId": "1133", "endLine": 278, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1174", "line": 467, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 467, "endColumn": 23}, {"ruleId": "1144", "severity": 1, "message": "1175", "line": 23, "column": 8, "nodeType": "1146", "endLine": 23, "endColumn": 25, "suggestions": "1176"}, {"ruleId": "1144", "severity": 1, "message": "1177", "line": 23, "column": 6, "nodeType": "1146", "endLine": 23, "endColumn": 44, "suggestions": "1178"}, {"ruleId": "1130", "severity": 1, "message": "1179", "line": 1, "column": 38, "nodeType": "1132", "messageId": "1133", "endLine": 1, "endColumn": 49}, {"ruleId": "1130", "severity": 1, "message": "1180", "line": 10, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 10, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1181", "line": 52, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 52, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1182", "line": 267, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 267, "endColumn": 40}, {"ruleId": "1130", "severity": 1, "message": "1183", "line": 704, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 704, "endColumn": 32}, {"ruleId": "1130", "severity": 1, "message": "1184", "line": 740, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 740, "endColumn": 30}, {"ruleId": "1130", "severity": 1, "message": "1163", "line": 19, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 19, "endColumn": 19}, {"ruleId": "1144", "severity": 1, "message": "1185", "line": 163, "column": 8, "nodeType": "1146", "endLine": 163, "endColumn": 26, "suggestions": "1186"}, {"ruleId": "1130", "severity": 1, "message": "1187", "line": 183, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 183, "endColumn": 31}, {"ruleId": "1130", "severity": 1, "message": "1188", "line": 6, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 6, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1189", "line": 15, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 15, "endColumn": 17}, {"ruleId": "1144", "severity": 1, "message": "1190", "line": 80, "column": 6, "nodeType": "1146", "endLine": 80, "endColumn": 8, "suggestions": "1191"}, {"ruleId": "1144", "severity": 1, "message": "1190", "line": 88, "column": 6, "nodeType": "1146", "endLine": 88, "endColumn": 14, "suggestions": "1192"}, {"ruleId": "1130", "severity": 1, "message": "1193", "line": 13, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1194", "line": 12, "column": 7, "nodeType": "1132", "messageId": "1133", "endLine": 12, "endColumn": 36}, {"ruleId": "1144", "severity": 1, "message": "1195", "line": 109, "column": 8, "nodeType": "1146", "endLine": 109, "endColumn": 10, "suggestions": "1196"}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 170, "column": 21, "nodeType": "1150", "messageId": "1151", "endLine": 183, "endColumn": 22}, {"ruleId": "1148", "severity": 1, "message": "1149", "line": 477, "column": 9, "nodeType": "1150", "messageId": "1151", "endLine": 556, "endColumn": 10}, {"ruleId": "1144", "severity": 1, "message": "1197", "line": 20, "column": 6, "nodeType": "1146", "endLine": 20, "endColumn": 40, "suggestions": "1198"}, {"ruleId": "1130", "severity": 1, "message": "1199", "line": 5, "column": 22, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 32}, {"ruleId": "1130", "severity": 1, "message": "1200", "line": 45, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 45, "endColumn": 20}, {"ruleId": "1130", "severity": 1, "message": "1201", "line": 5, "column": 98, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 117}, {"ruleId": "1144", "severity": 1, "message": "1202", "line": 351, "column": 6, "nodeType": "1146", "endLine": 351, "endColumn": 20, "suggestions": "1203"}, {"ruleId": "1130", "severity": 1, "message": "1204", "line": 26, "column": 18, "nodeType": "1132", "messageId": "1133", "endLine": 26, "endColumn": 25}, {"ruleId": "1130", "severity": 1, "message": "1205", "line": 27, "column": 19, "nodeType": "1132", "messageId": "1133", "endLine": 27, "endColumn": 27}, {"ruleId": "1130", "severity": 1, "message": "1206", "line": 42, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 42, "endColumn": 27}, {"ruleId": "1144", "severity": 1, "message": "1207", "line": 66, "column": 8, "nodeType": "1146", "endLine": 66, "endColumn": 21, "suggestions": "1208"}, {"ruleId": "1130", "severity": 1, "message": "1209", "line": 268, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 268, "endColumn": 33}, {"ruleId": "1130", "severity": 1, "message": "1210", "line": 282, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 282, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1211", "line": 14, "column": 5, "nodeType": "1132", "messageId": "1133", "endLine": 14, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 9, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 9, "endColumn": 14}, {"ruleId": "1144", "severity": 1, "message": "1145", "line": 90, "column": 6, "nodeType": "1146", "endLine": 90, "endColumn": 8, "suggestions": "1213"}, {"ruleId": "1130", "severity": 1, "message": "1214", "line": 43, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 43, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1215", "line": 22, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 22, "endColumn": 20}, {"ruleId": "1144", "severity": 1, "message": "1216", "line": 94, "column": 6, "nodeType": "1146", "endLine": 94, "endColumn": 27, "suggestions": "1217"}, {"ruleId": "1144", "severity": 1, "message": "1218", "line": 102, "column": 6, "nodeType": "1146", "endLine": 102, "endColumn": 27, "suggestions": "1219"}, {"ruleId": "1130", "severity": 1, "message": "1220", "line": 25, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 25, "endColumn": 25}, {"ruleId": "1130", "severity": 1, "message": "1221", "line": 25, "column": 27, "nodeType": "1132", "messageId": "1133", "endLine": 25, "endColumn": 45}, {"ruleId": "1130", "severity": 1, "message": "1222", "line": 426, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 426, "endColumn": 25}, {"ruleId": "1130", "severity": 1, "message": "1223", "line": 456, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 456, "endColumn": 29}, {"ruleId": "1144", "severity": 1, "message": "1224", "line": 483, "column": 6, "nodeType": "1146", "endLine": 483, "endColumn": 76, "suggestions": "1225"}, {"ruleId": "1130", "severity": 1, "message": "1226", "line": 40, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 40, "endColumn": 17}, {"ruleId": "1144", "severity": 1, "message": "1227", "line": 80, "column": 6, "nodeType": "1146", "endLine": 80, "endColumn": 17, "suggestions": "1228"}, {"ruleId": "1130", "severity": 1, "message": "1229", "line": 5, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 16}, {"ruleId": "1130", "severity": 1, "message": "1230", "line": 11, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 11, "endColumn": 28}, {"ruleId": "1130", "severity": 1, "message": "1231", "line": 11, "column": 30, "nodeType": "1132", "messageId": "1133", "endLine": 11, "endColumn": 51}, {"ruleId": "1130", "severity": 1, "message": "1232", "line": 5, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 24}, {"ruleId": "1144", "severity": 1, "message": "1233", "line": 15, "column": 6, "nodeType": "1146", "endLine": 15, "endColumn": 20, "suggestions": "1234"}, {"ruleId": "1130", "severity": 1, "message": "1235", "line": 20, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 20, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1236", "line": 20, "column": 26, "nodeType": "1132", "messageId": "1133", "endLine": 20, "endColumn": 43}, {"ruleId": "1144", "severity": 1, "message": "1237", "line": 33, "column": 6, "nodeType": "1146", "endLine": 33, "endColumn": 16, "suggestions": "1238"}, {"ruleId": "1144", "severity": 1, "message": "1239", "line": 44, "column": 6, "nodeType": "1146", "endLine": 44, "endColumn": 18, "suggestions": "1240"}, {"ruleId": "1130", "severity": 1, "message": "1241", "line": 185, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 185, "endColumn": 35}, {"ruleId": "1130", "severity": 1, "message": "1242", "line": 3, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1243", "line": 3, "column": 51, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 61}, {"ruleId": "1130", "severity": 1, "message": "1244", "line": 4, "column": 3, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 12}, {"ruleId": "1130", "severity": 1, "message": "1245", "line": 4, "column": 14, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1246", "line": 4, "column": 54, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 60}, {"ruleId": "1144", "severity": 1, "message": "1247", "line": 89, "column": 6, "nodeType": "1146", "endLine": 89, "endColumn": 16, "suggestions": "1248"}, {"ruleId": "1130", "severity": 1, "message": "1249", "line": 151, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 151, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1250", "line": 184, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 184, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1251", "line": 240, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 240, "endColumn": 25}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 582, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 582, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 582, "column": 143, "nodeType": "1254", "messageId": "1255", "endLine": 582, "endColumn": 145}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 586, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 586, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 586, "column": 148, "nodeType": "1254", "messageId": "1255", "endLine": 586, "endColumn": 150}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 590, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 590, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 590, "column": 146, "nodeType": "1254", "messageId": "1255", "endLine": 590, "endColumn": 148}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 594, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 594, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 594, "column": 144, "nodeType": "1254", "messageId": "1255", "endLine": 594, "endColumn": 146}, {"ruleId": "1130", "severity": 1, "message": "1256", "line": 5, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 25}, {"ruleId": "1130", "severity": 1, "message": "1257", "line": 12, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 12, "endColumn": 18}, {"ruleId": "1144", "severity": 1, "message": "1258", "line": 55, "column": 6, "nodeType": "1146", "endLine": 55, "endColumn": 8, "suggestions": "1259"}, {"ruleId": "1130", "severity": 1, "message": "1260", "line": 1, "column": 27, "nodeType": "1132", "messageId": "1133", "endLine": 1, "endColumn": 36}, {"ruleId": "1130", "severity": 1, "message": "1261", "line": 3, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 19}, {"ruleId": "1144", "severity": 1, "message": "1262", "line": 60, "column": 6, "nodeType": "1146", "endLine": 60, "endColumn": 29, "suggestions": "1263"}, {"ruleId": "1130", "severity": 1, "message": "1264", "line": 27, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 27, "endColumn": 28}, {"ruleId": "1130", "severity": 1, "message": "1265", "line": 28, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 28, "endColumn": 32}, {"ruleId": "1144", "severity": 1, "message": "1266", "line": 48, "column": 8, "nodeType": "1146", "endLine": 48, "endColumn": 63, "suggestions": "1267"}, {"ruleId": "1130", "severity": 1, "message": "1268", "line": 14, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 14, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1269", "line": 15, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 15, "endColumn": 26}, {"ruleId": "1144", "severity": 1, "message": "1270", "line": 47, "column": 8, "nodeType": "1146", "endLine": 47, "endColumn": 88, "suggestions": "1271"}, {"ruleId": "1130", "severity": 1, "message": "1272", "line": 8, "column": 3, "nodeType": "1132", "messageId": "1133", "endLine": 8, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1273", "line": 21, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 21, "endColumn": 27}, {"ruleId": "1130", "severity": 1, "message": "1214", "line": 217, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 217, "endColumn": 19}, {"ruleId": "1144", "severity": 1, "message": "1274", "line": 26, "column": 6, "nodeType": "1146", "endLine": 26, "endColumn": 48, "suggestions": "1275"}, {"ruleId": "1144", "severity": 1, "message": "1276", "line": 26, "column": 6, "nodeType": "1146", "endLine": 26, "endColumn": 48, "suggestions": "1277"}, {"ruleId": "1130", "severity": 1, "message": "1278", "line": 3, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 14}, {"ruleId": "1144", "severity": 1, "message": "1279", "line": 27, "column": 6, "nodeType": "1146", "endLine": 27, "endColumn": 62, "suggestions": "1280"}, {"ruleId": "1130", "severity": 1, "message": "1281", "line": 21, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 21, "endColumn": 23}, {"ruleId": "1144", "severity": 1, "message": "1282", "line": 88, "column": 6, "nodeType": "1146", "endLine": 88, "endColumn": 40, "suggestions": "1283"}, {"ruleId": "1144", "severity": 1, "message": "1284", "line": 54, "column": 27, "nodeType": "1132", "endLine": 54, "endColumn": 38}, {"ruleId": "1144", "severity": 1, "message": "1285", "line": 86, "column": 6, "nodeType": "1146", "endLine": 86, "endColumn": 12, "suggestions": "1286"}, {"ruleId": "1130", "severity": 1, "message": "1287", "line": 5, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 7, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 14}, {"ruleId": "1144", "severity": 1, "message": "1288", "line": 107, "column": 8, "nodeType": "1146", "endLine": 107, "endColumn": 39, "suggestions": "1289"}, {"ruleId": "1144", "severity": 1, "message": "1202", "line": 74, "column": 8, "nodeType": "1146", "endLine": 74, "endColumn": 46, "suggestions": "1290"}, {"ruleId": "1130", "severity": 1, "message": "1214", "line": 43, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 43, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1291", "line": 5, "column": 57, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 77}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 6, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 6, "endColumn": 14}, {"ruleId": "1144", "severity": 1, "message": "1292", "line": 30, "column": 6, "nodeType": "1146", "endLine": 30, "endColumn": 31, "suggestions": "1293"}, {"ruleId": "1130", "severity": 1, "message": "1215", "line": 22, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 22, "endColumn": 20}, {"ruleId": "1144", "severity": 1, "message": "1216", "line": 94, "column": 6, "nodeType": "1146", "endLine": 94, "endColumn": 27, "suggestions": "1294"}, {"ruleId": "1144", "severity": 1, "message": "1218", "line": 102, "column": 6, "nodeType": "1146", "endLine": 102, "endColumn": 27, "suggestions": "1295"}, {"ruleId": "1144", "severity": 1, "message": "1296", "line": 239, "column": 6, "nodeType": "1146", "endLine": 239, "endColumn": 49, "suggestions": "1297"}, {"ruleId": "1130", "severity": 1, "message": "1298", "line": 658, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 658, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1299", "line": 709, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 709, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1300", "line": 835, "column": 19, "nodeType": "1132", "messageId": "1133", "endLine": 835, "endColumn": 29}, {"ruleId": "1130", "severity": 1, "message": "1301", "line": 838, "column": 19, "nodeType": "1132", "messageId": "1133", "endLine": 838, "endColumn": 32}, {"ruleId": "1302", "severity": 1, "message": "1303", "line": 917, "column": 27, "nodeType": "1304", "endLine": 922, "endColumn": 29}, {"ruleId": "1144", "severity": 1, "message": "1218", "line": 39, "column": 6, "nodeType": "1146", "endLine": 39, "endColumn": 8, "suggestions": "1305"}, {"ruleId": "1144", "severity": 1, "message": "1218", "line": 48, "column": 6, "nodeType": "1146", "endLine": 48, "endColumn": 8, "suggestions": "1306"}, {"ruleId": "1130", "severity": 1, "message": "1307", "line": 584, "column": 13, "nodeType": "1132", "messageId": "1133", "endLine": 584, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1308", "line": 12, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 12, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1309", "line": 12, "column": 21, "nodeType": "1132", "messageId": "1133", "endLine": 12, "endColumn": 33}, {"ruleId": "1144", "severity": 1, "message": "1218", "line": 35, "column": 6, "nodeType": "1146", "endLine": 35, "endColumn": 8, "suggestions": "1310"}, {"ruleId": "1144", "severity": 1, "message": "1218", "line": 44, "column": 6, "nodeType": "1146", "endLine": 44, "endColumn": 8, "suggestions": "1311"}, {"ruleId": "1130", "severity": 1, "message": "1308", "line": 15, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 15, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1309", "line": 15, "column": 21, "nodeType": "1132", "messageId": "1133", "endLine": 15, "endColumn": 33}, {"ruleId": "1130", "severity": 1, "message": "1312", "line": 157, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 157, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1313", "line": 3, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 15}, {"ruleId": "1130", "severity": 1, "message": "1168", "line": 15, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 15, "endColumn": 17}, {"ruleId": "1130", "severity": 1, "message": "1152", "line": 7, "column": 22, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 32}, {"ruleId": "1130", "severity": 1, "message": "1278", "line": 2, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 2, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1314", "line": 6, "column": 39, "nodeType": "1132", "messageId": "1133", "endLine": 6, "endColumn": 51}, {"ruleId": "1130", "severity": 1, "message": "1315", "line": 13, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1316", "line": 13, "column": 25, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 39}, {"ruleId": "1130", "severity": 1, "message": "1317", "line": 124, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 124, "endColumn": 30}, {"ruleId": "1130", "severity": 1, "message": "1318", "line": 235, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 235, "endColumn": 28}, {"ruleId": "1130", "severity": 1, "message": "1319", "line": 247, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 247, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1320", "line": 255, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 255, "endColumn": 23}, {"ruleId": "1144", "severity": 1, "message": "1321", "line": 35, "column": 6, "nodeType": "1146", "endLine": 35, "endColumn": 17, "suggestions": "1322"}, {"ruleId": "1144", "severity": 1, "message": "1323", "line": 45, "column": 6, "nodeType": "1146", "endLine": 45, "endColumn": 17, "suggestions": "1324"}, {"ruleId": "1144", "severity": 1, "message": "1325", "line": 38, "column": 6, "nodeType": "1146", "endLine": 38, "endColumn": 17, "suggestions": "1326"}, {"ruleId": "1144", "severity": 1, "message": "1327", "line": 38, "column": 6, "nodeType": "1146", "endLine": 38, "endColumn": 17, "suggestions": "1328"}, {"ruleId": "1144", "severity": 1, "message": "1329", "line": 35, "column": 6, "nodeType": "1146", "endLine": 35, "endColumn": 17, "suggestions": "1330"}, {"ruleId": "1130", "severity": 1, "message": "1313", "line": 6, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 6, "endColumn": 15}, {"ruleId": "1130", "severity": 1, "message": "1331", "line": 7, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 20}, {"ruleId": "1144", "severity": 1, "message": "1332", "line": 32, "column": 6, "nodeType": "1146", "endLine": 32, "endColumn": 15, "suggestions": "1333"}, {"ruleId": "1130", "severity": 1, "message": "1334", "line": 87, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 87, "endColumn": 21}, {"ruleId": "1130", "severity": 1, "message": "1335", "line": 217, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 217, "endColumn": 26}, {"ruleId": "1144", "severity": 1, "message": "1336", "line": 10, "column": 6, "nodeType": "1146", "endLine": 10, "endColumn": 8, "suggestions": "1337"}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 4, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1314", "line": 6, "column": 39, "nodeType": "1132", "messageId": "1133", "endLine": 6, "endColumn": 51}, {"ruleId": "1130", "severity": 1, "message": "1315", "line": 13, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1316", "line": 13, "column": 25, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 39}, {"ruleId": "1130", "severity": 1, "message": "1317", "line": 124, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 124, "endColumn": 30}, {"ruleId": "1130", "severity": 1, "message": "1318", "line": 235, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 235, "endColumn": 28}, {"ruleId": "1130", "severity": 1, "message": "1319", "line": 247, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 247, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1320", "line": 255, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 255, "endColumn": 23}, {"ruleId": "1144", "severity": 1, "message": "1338", "line": 88, "column": 8, "nodeType": "1146", "endLine": 88, "endColumn": 10, "suggestions": "1339"}, {"ruleId": "1144", "severity": 1, "message": "1338", "line": 92, "column": 8, "nodeType": "1146", "endLine": 92, "endColumn": 14, "suggestions": "1340"}, {"ruleId": "1144", "severity": 1, "message": "1341", "line": 33, "column": 6, "nodeType": "1146", "endLine": 33, "endColumn": 21, "suggestions": "1342"}, {"ruleId": "1130", "severity": 1, "message": "1343", "line": 173, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 173, "endColumn": 27}, {"ruleId": "1130", "severity": 1, "message": "1344", "line": 93, "column": 18, "nodeType": "1132", "messageId": "1133", "endLine": 93, "endColumn": 37}, {"ruleId": "1144", "severity": 1, "message": "1338", "line": 22, "column": 6, "nodeType": "1146", "endLine": 22, "endColumn": 8, "suggestions": "1345"}, {"ruleId": "1144", "severity": 1, "message": "1338", "line": 36, "column": 6, "nodeType": "1146", "endLine": 36, "endColumn": 32, "suggestions": "1346"}, {"ruleId": "1144", "severity": 1, "message": "1347", "line": 42, "column": 6, "nodeType": "1146", "endLine": 42, "endColumn": 23, "suggestions": "1348"}, {"ruleId": "1130", "severity": 1, "message": "1349", "line": 75, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 75, "endColumn": 26}, {"ruleId": "1144", "severity": 1, "message": "1350", "line": 163, "column": 6, "nodeType": "1146", "endLine": 163, "endColumn": 33, "suggestions": "1351"}, {"ruleId": "1144", "severity": 1, "message": "1258", "line": 187, "column": 6, "nodeType": "1146", "endLine": 187, "endColumn": 8, "suggestions": "1352"}, {"ruleId": "1353", "severity": 1, "message": "1354", "line": 77, "column": 13, "nodeType": "1304", "endLine": 77, "endColumn": 42}, {"ruleId": "1130", "severity": 1, "message": "1355", "line": 15, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 15, "endColumn": 24}, {"ruleId": "1144", "severity": 1, "message": "1356", "line": 50, "column": 6, "nodeType": "1146", "endLine": 50, "endColumn": 39, "suggestions": "1357"}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 4, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1358", "line": 30, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 30, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1214", "line": 21, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 21, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1359", "line": 67, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 67, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1214", "line": 80, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 80, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 6, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 6, "endColumn": 14}, {"ruleId": "1130", "severity": 1, "message": "1153", "line": 21, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 21, "endColumn": 22}, {"ruleId": "1144", "severity": 1, "message": "1171", "line": 39, "column": 8, "nodeType": "1146", "endLine": 39, "endColumn": 85, "suggestions": "1360"}, {"ruleId": "1130", "severity": 1, "message": "1361", "line": 155, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 155, "endColumn": 23}, {"ruleId": "1144", "severity": 1, "message": "1362", "line": 63, "column": 6, "nodeType": "1146", "endLine": 63, "endColumn": 73, "suggestions": "1363"}, {"ruleId": "1353", "severity": 1, "message": "1364", "line": 184, "column": 13, "nodeType": "1304", "endLine": 188, "endColumn": 15}, {"ruleId": "1144", "severity": 1, "message": "1365", "line": 38, "column": 6, "nodeType": "1146", "endLine": 38, "endColumn": 25, "suggestions": "1366"}, {"ruleId": "1130", "severity": 1, "message": "1367", "line": 5, "column": 55, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 68}, {"ruleId": "1144", "severity": 1, "message": "1368", "line": 20, "column": 6, "nodeType": "1146", "endLine": 20, "endColumn": 16, "suggestions": "1369"}, {"ruleId": "1130", "severity": 1, "message": "1331", "line": 7, "column": 14, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 24}, {"ruleId": "1144", "severity": 1, "message": "1332", "line": 32, "column": 10, "nodeType": "1146", "endLine": 32, "endColumn": 19, "suggestions": "1370"}, {"ruleId": "1130", "severity": 1, "message": "1334", "line": 87, "column": 13, "nodeType": "1132", "messageId": "1133", "endLine": 87, "endColumn": 25}, {"ruleId": "1130", "severity": 1, "message": "1335", "line": 217, "column": 13, "nodeType": "1132", "messageId": "1133", "endLine": 217, "endColumn": 30}, {"ruleId": "1130", "severity": 1, "message": "1371", "line": 56, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 56, "endColumn": 20}, {"ruleId": "1130", "severity": 1, "message": "1372", "line": 89, "column": 25, "nodeType": "1132", "messageId": "1133", "endLine": 89, "endColumn": 41}, {"ruleId": "1130", "severity": 1, "message": "1373", "line": 93, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 93, "endColumn": 26}, {"ruleId": "1144", "severity": 1, "message": "1374", "line": 124, "column": 6, "nodeType": "1146", "endLine": 124, "endColumn": 60, "suggestions": "1375"}, {"ruleId": "1130", "severity": 1, "message": "1376", "line": 278, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 278, "endColumn": 33}, {"ruleId": "1130", "severity": 1, "message": "1377", "line": 52, "column": 13, "nodeType": "1132", "messageId": "1133", "endLine": 52, "endColumn": 21}, {"ruleId": "1144", "severity": 1, "message": "1378", "line": 23, "column": 8, "nodeType": "1146", "endLine": 23, "endColumn": 10, "suggestions": "1379"}, {"ruleId": "1130", "severity": 1, "message": "1380", "line": 94, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 94, "endColumn": 23}, {"ruleId": "1130", "severity": 1, "message": "1215", "line": 18, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 18, "endColumn": 20}, {"ruleId": "1144", "severity": 1, "message": "1362", "line": 43, "column": 6, "nodeType": "1146", "endLine": 43, "endColumn": 61, "suggestions": "1381"}, {"ruleId": "1130", "severity": 1, "message": "1382", "line": 628, "column": 39, "nodeType": "1132", "messageId": "1133", "endLine": 628, "endColumn": 52}, {"ruleId": "1130", "severity": 1, "message": "1383", "line": 629, "column": 11, "nodeType": "1132", "messageId": "1133", "endLine": 629, "endColumn": 19}, {"ruleId": "1130", "severity": 1, "message": "1138", "line": 7, "column": 32, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 42}, {"ruleId": "1144", "severity": 1, "message": "1384", "line": 71, "column": 6, "nodeType": "1146", "endLine": 71, "endColumn": 45, "suggestions": "1385"}, {"ruleId": "1144", "severity": 1, "message": "1386", "line": 127, "column": 6, "nodeType": "1146", "endLine": 127, "endColumn": 8, "suggestions": "1387"}, {"ruleId": "1130", "severity": 1, "message": "1388", "line": 13, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 14}, {"ruleId": "1144", "severity": 1, "message": "1389", "line": 70, "column": 6, "nodeType": "1146", "endLine": 70, "endColumn": 24, "suggestions": "1390"}, {"ruleId": "1130", "severity": 1, "message": "1212", "line": 7, "column": 8, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 14}, {"ruleId": "1144", "severity": 1, "message": "1391", "line": 61, "column": 6, "nodeType": "1146", "endLine": 61, "endColumn": 22, "suggestions": "1392"}, {"ruleId": "1130", "severity": 1, "message": "1242", "line": 3, "column": 12, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1243", "line": 3, "column": 51, "nodeType": "1132", "messageId": "1133", "endLine": 3, "endColumn": 61}, {"ruleId": "1130", "severity": 1, "message": "1244", "line": 4, "column": 3, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 12}, {"ruleId": "1130", "severity": 1, "message": "1245", "line": 4, "column": 14, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 24}, {"ruleId": "1130", "severity": 1, "message": "1246", "line": 4, "column": 54, "nodeType": "1132", "messageId": "1133", "endLine": 4, "endColumn": 60}, {"ruleId": "1130", "severity": 1, "message": "1313", "line": 7, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 15}, {"ruleId": "1130", "severity": 1, "message": "1199", "line": 12, "column": 22, "nodeType": "1132", "messageId": "1133", "endLine": 12, "endColumn": 32}, {"ruleId": "1130", "severity": 1, "message": "1393", "line": 13, "column": 34, "nodeType": "1132", "messageId": "1133", "endLine": 13, "endColumn": 46}, {"ruleId": "1130", "severity": 1, "message": "1394", "line": 41, "column": 23, "nodeType": "1132", "messageId": "1133", "endLine": 41, "endColumn": 37}, {"ruleId": "1130", "severity": 1, "message": "1395", "line": 42, "column": 27, "nodeType": "1132", "messageId": "1133", "endLine": 42, "endColumn": 45}, {"ruleId": "1144", "severity": 1, "message": "1247", "line": 63, "column": 6, "nodeType": "1146", "endLine": 63, "endColumn": 16, "suggestions": "1396"}, {"ruleId": "1130", "severity": 1, "message": "1249", "line": 168, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 168, "endColumn": 22}, {"ruleId": "1130", "severity": 1, "message": "1250", "line": 201, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 201, "endColumn": 26}, {"ruleId": "1130", "severity": 1, "message": "1251", "line": 271, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 271, "endColumn": 25}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 610, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 610, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 610, "column": 143, "nodeType": "1254", "messageId": "1255", "endLine": 610, "endColumn": 145}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 614, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 614, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 614, "column": 148, "nodeType": "1254", "messageId": "1255", "endLine": 614, "endColumn": 150}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 618, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 618, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 618, "column": 146, "nodeType": "1254", "messageId": "1255", "endLine": 618, "endColumn": 148}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 622, "column": 54, "nodeType": "1254", "messageId": "1255", "endLine": 622, "endColumn": 56}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 622, "column": 144, "nodeType": "1254", "messageId": "1255", "endLine": 622, "endColumn": 146}, {"ruleId": "1130", "severity": 1, "message": "1313", "line": 7, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 7, "endColumn": 15}, {"ruleId": "1144", "severity": 1, "message": "1397", "line": 154, "column": 6, "nodeType": "1146", "endLine": 154, "endColumn": 43, "suggestions": "1398"}, {"ruleId": "1144", "severity": 1, "message": "1399", "line": 248, "column": 9, "nodeType": "1400", "endLine": 293, "endColumn": 4, "suggestions": "1401"}, {"ruleId": "1144", "severity": 1, "message": "1402", "line": 248, "column": 9, "nodeType": "1400", "endLine": 293, "endColumn": 4, "suggestions": "1403"}, {"ruleId": "1144", "severity": 1, "message": "1404", "line": 295, "column": 9, "nodeType": "1400", "endLine": 329, "endColumn": 4, "suggestions": "1405"}, {"ruleId": "1130", "severity": 1, "message": "1313", "line": 5, "column": 10, "nodeType": "1132", "messageId": "1133", "endLine": 5, "endColumn": 15}, {"ruleId": "1130", "severity": 1, "message": "1406", "line": 39, "column": 9, "nodeType": "1132", "messageId": "1133", "endLine": 39, "endColumn": 19}, {"ruleId": "1407", "severity": 2, "message": "1408", "line": 6, "column": 33, "nodeType": "1132", "endLine": 6, "endColumn": 41}, {"ruleId": "1407", "severity": 2, "message": "1408", "line": 7, "column": 43, "nodeType": "1132", "endLine": 7, "endColumn": 51}, {"ruleId": "1144", "severity": 1, "message": "1409", "line": 131, "column": 6, "nodeType": "1146", "endLine": 131, "endColumn": 28, "suggestions": "1410"}, {"ruleId": "1144", "severity": 1, "message": "1411", "line": 215, "column": 9, "nodeType": "1400", "endLine": 260, "endColumn": 4, "suggestions": "1412"}, {"ruleId": "1144", "severity": 1, "message": "1413", "line": 215, "column": 9, "nodeType": "1400", "endLine": 260, "endColumn": 4, "suggestions": "1414"}, {"ruleId": "1144", "severity": 1, "message": "1415", "line": 415, "column": 9, "nodeType": "1400", "endLine": 449, "endColumn": 4, "suggestions": "1416"}, {"ruleId": "1144", "severity": 1, "message": "1417", "line": 28, "column": 6, "nodeType": "1146", "endLine": 28, "endColumn": 14, "suggestions": "1418"}, "no-unused-vars", "'Navigate' is defined but never used.", "Identifier", "unusedVar", "'lazy' is defined but never used.", "'PublicCourseDetails' is defined but never used.", "'Link' is defined but never used.", "'Footer' is defined but never used.", "'sendOTPApi' is defined but never used.", "'organization_id' is assigned a value but never used.", "'isFormValid' is assigned a value but never used.", "'setIsFormValid' is assigned a value but never used.", "'setClassroomData' is assigned a value but never used.", "'dashboardData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["1419"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'decodeData' is defined but never used.", "'totalPages' is assigned a value but never used.", "'totalRecords' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getAllClassroomData'. Either include it or remove the dependency array.", ["1420"], "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", ["1421"], "'handleUpdateAssignment' is assigned a value but never used.", "'dueStatus' is assigned a value but never used.", "'formatDateForInput' is assigned a value but never used.", "'formatToLocalString' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrainees'. Either include it or remove the dependency array.", ["1422"], "React Hook useEffect has a missing dependency: 'fetchAvailableTrainees'. Either include it or remove the dependency array.", ["1423"], "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseList'. Either include it or remove the dependency array.", ["1424"], "React Hook useEffect has a missing dependency: 'fetchLiveClasses'. Either include it or remove the dependency array.", ["1425"], "'response' is assigned a value but never used.", "'formatDateTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseStats'. Either include it or remove the dependency array.", ["1426"], "React Hook useEffect has a missing dependency: 'fetchAssessmentDetails'. Either include it or remove the dependency array.", ["1427"], "'useCallback' is defined but never used.", "'location' is assigned a value but never used.", "'correctAnswers' is assigned a value but never used.", "'handleOptionContentTypeChange' is assigned a value but never used.", "'renderQuestionContent' is assigned a value but never used.", "'renderOptionContent' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getDocumentDetailsById'. Either include it or remove the dependency array.", ["1428"], "'handleDocumentRemove' is assigned a value but never used.", "'Loader' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseModule'. Either include it or remove the dependency array.", ["1429"], ["1430"], "'decodedCourseId' is assigned a value but never used.", "'REACT_APP_BITMOVIN_PLAYER_KEY' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleContent'. Either include it or remove the dependency array.", ["1431"], "React Hook useEffect has a missing dependency: 'fetchSurveyDetails'. Either include it or remove the dependency array.", ["1432"], "'encodeData' is defined but never used.", "'newVideo' is assigned a value but never used.", "'getQuestionBankById' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1433"], "'setPage' is assigned a value but never used.", "'setLimit' is assigned a value but never used.", "'documentPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCertificates'. Either include it or remove the dependency array.", ["1434"], "'handleCreateModalClose' is assigned a value but never used.", "'togglePreview' is assigned a value but never used.", "'sendAnnouncements' is defined but never used.", "'NoData' is defined but never used.", ["1435"], "'formatDate' is assigned a value but never used.", "'totalCount' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'notifications.length'. Either include it or remove the dependency array.", ["1436"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["1437"], "'showDeleteModal' is assigned a value but never used.", "'setShowDeleteModal' is assigned a value but never used.", "'handleBulkUpload' is assigned a value but never used.", "'handleSampleDownload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTraineesData'. Either include it or remove the dependency array.", ["1438"], "'tabData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1439"], "'TodoList' is defined but never used.", "'monthlyPerformance' is assigned a value but never used.", "'setMonthlyPerformance' is assigned a value but never used.", "'changePassword' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchResults'. Either include it or remove the dependency array.", ["1440"], "'courseProgress' is assigned a value but never used.", "'setCourseProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleList'. Either include it or remove the dependency array.", ["1441"], "React Hook useEffect has a missing dependency: 'selectedContent'. Either include it or remove the dependency array.", ["1442"], "'handleCourseProgressUpdate' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaShareAlt' is defined but never used.", "'FaTwitter' is defined but never used.", "'FaLinkedin' is defined but never used.", "'FaLock' is defined but never used.", "React Hook useEffect has a missing dependency: 'getCourseDetails'. Either include it or remove the dependency array.", ["1443"], "'courseModules' is assigned a value but never used.", "'handleWatchCourse' is assigned a value but never used.", "'hasSubCategories' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'saveRecentVideo' is defined but never used.", "'videoId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initPlayer'. Either include it or remove the dependency array.", ["1444"], "'useEffect' is defined but never used.", "'AccountInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendtovalidation'. Either include it or remove the dependency array.", ["1445"], "'hasMoreQuestions' is assigned a value but never used.", "'availableCurrentPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentQuestions'. Either include it or remove the dependency array.", ["1446"], "'allResults' is assigned a value but never used.", "'groupedResults' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAnalyticsData' and 'fetchResults'. Either include them or remove the dependency array.", ["1447"], "'gradeClassroomAssignmentSubmission' is defined but never used.", "'assignmentDetails' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentData'. Either include it or remove the dependency array.", ["1448"], "React Hook useEffect has a missing dependency: 'fetchSurveyData'. Either include it or remove the dependency array.", ["1449"], "'Icon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTraineeProgress'. Either include it or remove the dependency array.", ["1450"], "'classroomName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classroom_id'. Either include it or remove the dependency array.", ["1451"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has missing dependencies: 'fetchClassrooms' and 'search'. Either include them or remove the dependency array.", ["1452"], "'selectedFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleSubmit' and 'timeLeft'. Either include them or remove the dependency array.", ["1453"], ["1454"], "'editOrganisationUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'getTicketData'. Either include it or remove the dependency array.", ["1455"], ["1456"], ["1457"], "React Hook useEffect has a missing dependency: 'currentUserId'. Either include it or remove the dependency array.", ["1458"], "'toggleEmojiPicker' is assigned a value but never used.", "'dislikeMessage' is assigned a value but never used.", "'likesArray' is assigned a value but never used.", "'dislikesArray' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", ["1459"], ["1460"], "'moduleIndex' is assigned a value but never used.", "'collapsed' is assigned a value but never used.", "'setCollapsed' is assigned a value but never used.", ["1461"], ["1462"], "'classroomId' is assigned a value but never used.", "'toast' is defined but never used.", "'validateDate' is defined but never used.", "'profileData' is assigned a value but never used.", "'setProfileData' is assigned a value but never used.", "'handleCountrySelect' is assigned a value but never used.", "'handleImageChange' is assigned a value but never used.", "'handleChange' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchClassroomData'. Either include it or remove the dependency array.", ["1463"], "React Hook useEffect has a missing dependency: 'fetchCourseData'. Either include it or remove the dependency array.", ["1464"], "React Hook useEffect has a missing dependency: 'fetchPaymentData'. Either include it or remove the dependency array.", ["1465"], "React Hook useEffect has a missing dependency: 'fetchTicketsData'. Either include it or remove the dependency array.", ["1466"], "React Hook useEffect has a missing dependency: 'fetchCertificatesData'. Either include it or remove the dependency array.", ["1467"], "'updateLike' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1468"], "'fetchReplies' is assigned a value but never used.", "'handleDeleteReply' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMonthlyPerformance'. Either include it or remove the dependency array.", ["1469"], "React Hook useEffect has a missing dependency: 'searchTerm'. Either include it or remove the dependency array.", ["1470"], ["1471"], "React Hook useEffect has a missing dependency: 'getAssessmentQuestions'. Either include it or remove the dependency array.", ["1472"], "'getScorePercentage' is assigned a value but never used.", "'fetchCourseProgress' is defined but never used.", ["1473"], ["1474"], "React Hook useEffect has a missing dependency: 'contentData'. Either include it or remove the dependency array.", ["1475"], "'saveVideoProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'contentData?.title'. Either include it or remove the dependency array.", ["1476"], ["1477"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'totalQuestions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSurveyQuestions'. Either include it or remove the dependency array.", ["1478"], "'filteredData' is assigned a value but never used.", "'getStatusClass' is assigned a value but never used.", ["1479"], "'clearFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchResources'. Either include it or remove the dependency array.", ["1480"], "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1481"], "'updateComment' is defined but never used.", "React Hook useEffect has missing dependencies: 'getNotesData' and 'videoId'. Either include them or remove the dependency array.", ["1482"], ["1483"], "'actionType' is assigned a value but never used.", "'setSearchCountry' is assigned a value but never used.", "'filteredCountries' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1484"], "'handleStatusFilterChange' is assigned a value but never used.", "'newState' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["1485"], "'getTaskBadge' is assigned a value but never used.", ["1486"], "'resource_name' is assigned a value but never used.", "'fileName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeZoomMeeting'. Either include it or remove the dependency array.", ["1487"], "React Hook useEffect has missing dependencies: 'courseId', 'sendToValidation', and 'txnId'. Either include them or remove the dependency array.", ["1488"], "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleCancelValidation'. Either include it or remove the dependency array.", ["1489"], "React Hook useEffect has missing dependencies: 'courseData?.banner_image', 'courseData?.course_desc', 'courseData?.course_id', 'courseData?.course_name', 'courseData?.course_price', 'courseData?.course_type', 'courseData?.currency', 'courseData?.discountCode', 'courseData?.discountValue', and 'courseData?.points'. Either include them or remove the dependency array.", ["1490"], "'AddMyCourses' is defined but never used.", "'setIsEnrolling' is assigned a value but never used.", "'setIsPaidEnrolling' is assigned a value but never used.", ["1491"], "React Hook useEffect has a missing dependency: 'currentPage'. Either include it or remove the dependency array.", ["1492"], "The 'handleSubmitComment' function makes the dependencies of useCallback Hook (at line 486) change on every render. To fix this, wrap the definition of 'handleSubmitComment' in its own useCallback() Hook.", "VariableDeclarator", ["1493"], "The 'handleSubmitComment' function makes the dependencies of useCallback Hook (at line 490) change on every render. To fix this, wrap the definition of 'handleSubmitComment' in its own useCallback() Hook.", ["1494"], "The 'handleEditComment' function makes the dependencies of useCallback Hook (at line 500) change on every render. To fix this, wrap the definition of 'handleEditComment' in its own useCallback() Hook.", ["1495"], "'mediaStyle' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useState\" is called in function \"feedPost\" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word \"use\".", "React Hook useEffect has a missing dependency: 'loadMorePosts'. Either include it or remove the dependency array.", ["1496"], "The 'handleSubmitComment' function makes the dependencies of useCallback Hook (at line 409) change on every render. To fix this, wrap the definition of 'handleSubmitComment' in its own useCallback() Hook.", ["1497"], "The 'handleSubmitComment' function makes the dependencies of useCallback Hook (at line 413) change on every render. To fix this, wrap the definition of 'handleSubmitComment' in its own useCallback() Hook.", ["1498"], "The 'handleEditComment' function makes the dependencies of useCallback Hook (at line 490) change on every render. To fix this, wrap the definition of 'handleEditComment' in its own useCallback() Hook.", ["1499"], "React Hook useEffect has a missing dependency: 'fetchPostDetails'. Either include it or remove the dependency array.", ["1500"], {"desc": "1501", "fix": "1502"}, {"desc": "1503", "fix": "1504"}, {"desc": "1505", "fix": "1506"}, {"desc": "1507", "fix": "1508"}, {"desc": "1509", "fix": "1510"}, {"desc": "1511", "fix": "1512"}, {"desc": "1513", "fix": "1514"}, {"desc": "1515", "fix": "1516"}, {"desc": "1517", "fix": "1518"}, {"desc": "1519", "fix": "1520"}, {"desc": "1521", "fix": "1522"}, {"desc": "1523", "fix": "1524"}, {"desc": "1525", "fix": "1526"}, {"desc": "1527", "fix": "1528"}, {"desc": "1529", "fix": "1530"}, {"desc": "1531", "fix": "1532"}, {"desc": "1501", "fix": "1533"}, {"desc": "1534", "fix": "1535"}, {"desc": "1536", "fix": "1537"}, {"desc": "1538", "fix": "1539"}, {"desc": "1540", "fix": "1541"}, {"desc": "1542", "fix": "1543"}, {"desc": "1544", "fix": "1545"}, {"desc": "1546", "fix": "1547"}, {"desc": "1548", "fix": "1549"}, {"desc": "1550", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"desc": "1554", "fix": "1555"}, {"desc": "1556", "fix": "1557"}, {"desc": "1558", "fix": "1559"}, {"desc": "1560", "fix": "1561"}, {"desc": "1562", "fix": "1563"}, {"desc": "1564", "fix": "1565"}, {"desc": "1566", "fix": "1567"}, {"desc": "1568", "fix": "1569"}, {"desc": "1570", "fix": "1571"}, {"desc": "1572", "fix": "1573"}, {"desc": "1534", "fix": "1574"}, {"desc": "1536", "fix": "1575"}, {"desc": "1576", "fix": "1577"}, {"desc": "1578", "fix": "1579"}, {"desc": "1578", "fix": "1580"}, {"desc": "1578", "fix": "1581"}, {"desc": "1578", "fix": "1582"}, {"desc": "1583", "fix": "1584"}, {"desc": "1585", "fix": "1586"}, {"desc": "1587", "fix": "1588"}, {"desc": "1589", "fix": "1590"}, {"desc": "1591", "fix": "1592"}, {"desc": "1593", "fix": "1594"}, {"desc": "1595", "fix": "1596"}, {"desc": "1597", "fix": "1598"}, {"desc": "1599", "fix": "1600"}, {"desc": "1601", "fix": "1602"}, {"desc": "1597", "fix": "1603"}, {"desc": "1604", "fix": "1605"}, {"desc": "1606", "fix": "1607"}, {"desc": "1608", "fix": "1609"}, {"desc": "1550", "fix": "1610"}, {"desc": "1611", "fix": "1612"}, {"desc": "1613", "fix": "1614"}, {"desc": "1615", "fix": "1616"}, {"desc": "1617", "fix": "1618"}, {"desc": "1619", "fix": "1620"}, {"desc": "1593", "fix": "1621"}, {"desc": "1622", "fix": "1623"}, {"desc": "1624", "fix": "1625"}, {"desc": "1626", "fix": "1627"}, {"desc": "1628", "fix": "1629"}, {"desc": "1630", "fix": "1631"}, {"desc": "1632", "fix": "1633"}, {"desc": "1634", "fix": "1635"}, {"desc": "1548", "fix": "1636"}, {"desc": "1637", "fix": "1638"}, {"desc": "1639", "fix": "1640"}, {"desc": "1639", "fix": "1641"}, {"desc": "1642", "fix": "1643"}, {"desc": "1644", "fix": "1645"}, {"desc": "1639", "fix": "1646"}, {"desc": "1639", "fix": "1647"}, {"desc": "1642", "fix": "1648"}, {"desc": "1649", "fix": "1650"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1651", "text": "1652"}, "Update the dependencies array to be: [currentPage, getAllClassroomData, itemsPerPage, searchQuery]", {"range": "1653", "text": "1654"}, "Update the dependencies array to be: [decodedClassroomId, fetchAssignments]", {"range": "1655", "text": "1656"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", {"range": "1657", "text": "1658"}, "Update the dependencies array to be: [fetchAvailableTrainees, modalSearchTerm, showAddModal]", {"range": "1659", "text": "1660"}, "Update the dependencies array to be: [fetchCourseList]", {"range": "1661", "text": "1662"}, "Update the dependencies array to be: [classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1663", "text": "1664"}, "Update the dependencies array to be: [decodedCourseId, fetchCourseStats]", {"range": "1665", "text": "1666"}, "Update the dependencies array to be: [decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", {"range": "1667", "text": "1668"}, "Update the dependencies array to be: [decodedContentId, getDocumentDetailsById]", {"range": "1669", "text": "1670"}, "Update the dependencies array to be: [fetchCourseModule]", {"range": "1671", "text": "1672"}, "Update the dependencies array to be: [fetchCourseModule, search]", {"range": "1673", "text": "1674"}, "Update the dependencies array to be: [fetchModuleContent]", {"range": "1675", "text": "1676"}, "Update the dependencies array to be: [decodedSurveyId, decodedCourseId, fetchSurveyDetails]", {"range": "1677", "text": "1678"}, "Update the dependencies array to be: [fetchQuestions, itemsPerPage]", {"range": "1679", "text": "1680"}, "Update the dependencies array to be: [page, limit, fetchCertificates]", {"range": "1681", "text": "1682"}, {"range": "1683", "text": "1652"}, "Update the dependencies array to be: [debouncedSearchTerm, notifications.length]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [debouncedSearchTerm, fetchNotifications]", {"range": "1686", "text": "1687"}, "Update the dependencies array to be: [pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", {"range": "1688", "text": "1689"}, "Update the dependencies array to be: [decodedId, fetchAnalyticsData]", {"range": "1690", "text": "1691"}, "Update the dependencies array to be: [assessmentId, fetchResults]", {"range": "1692", "text": "1693"}, "Update the dependencies array to be: [courseId, fetchModuleList]", {"range": "1694", "text": "1695"}, "Update the dependencies array to be: [moduleList, selectedContent]", {"range": "1696", "text": "1697"}, "Update the dependencies array to be: [courseId, getCourseDetails]", {"range": "1698", "text": "1699"}, "Update the dependencies array to be: [initPlayer]", {"range": "1700", "text": "1701"}, "Update the dependencies array to be: [course_id, sendtovalidation, session_id]", {"range": "1702", "text": "1703"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", {"range": "1704", "text": "1705"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", {"range": "1706", "text": "1707"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchAssessmentData]", {"range": "1708", "text": "1709"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchSurveyData]", {"range": "1710", "text": "1711"}, "Update the dependencies array to be: [decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", {"range": "1712", "text": "1713"}, "Update the dependencies array to be: [classroom_id, encodedClassroomID, urlActiveTab]", {"range": "1714", "text": "1715"}, "Update the dependencies array to be: [fetchClassrooms, page, search]", {"range": "1716", "text": "1717"}, "Update the dependencies array to be: [handleSubmit, quizStarted, submissionResult, timeLeft]", {"range": "1718", "text": "1719"}, "Update the dependencies array to be: [assignment_id, classroom_id, fetchQuestions, user_id]", {"range": "1720", "text": "1721"}, "Update the dependencies array to be: [page, limit, searchTerm, getTicketData]", {"range": "1722", "text": "1723"}, {"range": "1724", "text": "1685"}, {"range": "1725", "text": "1687"}, "Update the dependencies array to be: [classroom_id, currentUserId, group_id, group_name, token]", {"range": "1726", "text": "1727"}, "Update the dependencies array to be: [fetchNotifications]", {"range": "1728", "text": "1729"}, {"range": "1730", "text": "1729"}, {"range": "1731", "text": "1729"}, {"range": "1732", "text": "1729"}, "Update the dependencies array to be: [fetchClassroomData, traineeId]", {"range": "1733", "text": "1734"}, "Update the dependencies array to be: [fetchCourseData, traineeId]", {"range": "1735", "text": "1736"}, "Update the dependencies array to be: [fetchPaymentData, traineeId]", {"range": "1737", "text": "1738"}, "Update the dependencies array to be: [fetchTicketsData, traineeId]", {"range": "1739", "text": "1740"}, "Update the dependencies array to be: [fetchCertificatesData, traineeId]", {"range": "1741", "text": "1742"}, "Update the dependencies array to be: [fetchComments, videoId]", {"range": "1743", "text": "1744"}, "Update the dependencies array to be: [fetchMonthlyPerformance]", {"range": "1745", "text": "1746"}, "Update the dependencies array to be: [searchTerm]", {"range": "1747", "text": "1748"}, "Update the dependencies array to be: [page, searchTerm]", {"range": "1749", "text": "1750"}, "Update the dependencies array to be: [getAssessmentQuestions, moduleData.id]", {"range": "1751", "text": "1752"}, {"range": "1753", "text": "1748"}, "Update the dependencies array to be: [page, hasMore, isLoading, searchTerm]", {"range": "1754", "text": "1755"}, "Update the dependencies array to be: [contentData, contentData?.id]", {"range": "1756", "text": "1757"}, "Update the dependencies array to be: [videoUrl, contentData?.id, contentData?.title]", {"range": "1758", "text": "1759"}, {"range": "1760", "text": "1701"}, "Update the dependencies array to be: [surveyId, moduleData?.completed, fetchSurveyQuestions]", {"range": "1761", "text": "1762"}, "Update the dependencies array to be: [classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1763", "text": "1764"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", {"range": "1765", "text": "1766"}, "Update the dependencies array to be: [videoId, courseId, fetchReviews]", {"range": "1767", "text": "1768"}, "Update the dependencies array to be: [getNotesData, moduleId, videoId]", {"range": "1769", "text": "1770"}, {"range": "1771", "text": "1744"}, "Update the dependencies array to be: [currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", {"range": "1772", "text": "1773"}, "Update the dependencies array to be: [fetchTasks]", {"range": "1774", "text": "1775"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, selectedResourceType, fetchResources]", {"range": "1776", "text": "1777"}, "Update the dependencies array to be: [initializationStarted, loading, error, initializeZoomMeeting]", {"range": "1778", "text": "1779"}, "Update the dependencies array to be: [courseId, sendToValidation, txnId]", {"range": "1780", "text": "1781"}, "Update the dependencies array to be: [course_id, handleCancelValidation, txnid]", {"range": "1782", "text": "1783"}, "Update the dependencies array to be: [courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.currency, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", {"range": "1784", "text": "1785"}, {"range": "1786", "text": "1699"}, "Update the dependencies array to be: [loadMorePosts, loadingMore, hasMore, currentPage]", {"range": "1787", "text": "1788"}, "Wrap the definition of 'handleSubmitComment' in its own useCallback() Hook.", {"range": "1789", "text": "1790"}, {"range": "1791", "text": "1790"}, "Wrap the definition of 'handleEditComment' in its own useCallback() Hook.", {"range": "1792", "text": "1793"}, "Update the dependencies array to be: [loadingMore, hasMore, loadMorePosts]", {"range": "1794", "text": "1795"}, {"range": "1796", "text": "1797"}, {"range": "1798", "text": "1797"}, {"range": "1799", "text": "1793"}, "Update the dependencies array to be: [fetchPostDetails, postId]", {"range": "1800", "text": "1801"}, [8379, 8381], "[fetchDashboardData]", [4416, 4456], "[currentPage, getAllClassroomData, itemsPerPage, searchQuery]", [3270, 3290], "[decodedClassroomId, fetchAssignments]", [5315, 5374], "[decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", [5697, 5728], "[fetchAvailableTrainees, modalSearchTerm, showAddModal]", [2474, 2476], "[fetchCourseList]", [2868, 2944], "[classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [935, 952], "[decodedCourseId, fetchCourseStats]", [1024, 1062], "[decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", [5972, 5990], "[decodedContentId, getDocumentDetailsById]", [3092, 3094], "[fetchCourseModule]", [3269, 3277], "[fetchCourseModule, search]", [4264, 4266], "[fetchModuleContent]", [757, 791], "[decodedSurveyId, decodedCourseId, fetchSurveyDetails]", [10267, 10281], "[fetchQuestions, itemsPerPage]", [2486, 2499], "[page, limit, fetchCertificates]", [3277, 3279], [3318, 3339], "[debouncedSearchTerm, notifications.length]", [3597, 3618], "[debouncedSearchTerm, fetchNotifications]", [17596, 17666], "[pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", [2624, 2635], "[decodedId, fetchAnalyticsData]", [592, 606], "[assessmentId, fetchResults]", [1218, 1228], "[courseId, fetchModuleList]", [1678, 1690], "[module<PERSON>ist, <PERSON><PERSON><PERSON><PERSON>]", [2615, 2625], "[courseId, getCourseDetails]", [1876, 1878], "[initPlayer]", [1573, 1596], "[course_id, sendtovalidation, session_id]", [2503, 2558], "[decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", [2091, 2171], "[decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", [831, 873], "[decodedCourseId, search, pagination.page, fetchAssessmentData]", [806, 848], "[decodedCourseId, search, pagination.page, fetchSurveyData]", [920, 976], "[decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", [3577, 3611], "[classroom_id, encodedClassroomID, urlActiveTab]", [2869, 2875], "[fetchClassrooms, page, search]", [4500, 4531], "[handleSubmit, quizStarted, submissionResult, timeLeft]", [2728, 2766], "[assignment_id, classroom_id, fetchQuestions, user_id]", [1330, 1355], "[page, limit, searchTerm, getTicketData]", [3318, 3339], [3597, 3618], [8665, 8708], "[classroom_id, currentUserId, group_id, group_name, token]", [1557, 1559], "[fetchNotifications]", [1815, 1817], [1440, 1442], [1698, 1700], [1209, 1220], "[fetchClassroom<PERSON><PERSON>, traineeId]", [1666, 1677], "[fetchCourseData, traineeId]", [1330, 1341], "[fetchPayment<PERSON><PERSON>, traineeId]", [1325, 1336], "[fetchTickets<PERSON>ata, traineeId]", [1229, 1240], "[fetchCertificates<PERSON><PERSON>, traineeId]", [1367, 1376], "[fetchComments, videoId]", [393, 395], "[fetchMonthlyPerformance]", [3154, 3156], "[searchTerm]", [3248, 3254], "[page, searchTerm]", [1278, 1293], "[getAssessmentQuestions, moduleData.id]", [877, 879], [1304, 1330], "[page, hasMore, isLoading, searchTerm]", [1761, 1778], "[contentData, contentData?.id]", [6036, 6063], "[videoUrl, contentData?.id, contentData?.title]", [6796, 6798], [1804, 1837], "[surveyId, moduleData?.completed, fetchSurveyQuestions]", [1525, 1602], "[classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [2287, 2354], "[decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", [1270, 1289], "[videoId, courseId, fetchReviews]", [673, 683], "[getNotesData, moduleId, videoId]", [1475, 1484], [4256, 4310], "[currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", [824, 826], "[fetchTasks]", [1904, 1959], "[decodedClassroomId, currentPage, selectedResourceType, fetchResources]", [2406, 2445], "[initializationStarted, loading, error, initializeZoomMeeting]", [3808, 3810], "[courseId, sendToValidation, txnId]", [2264, 2282], "[course_id, handleCancelValidation, txnid]", [1850, 1866], "[courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.currency, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", [1901, 1911], [5617, 5654], "[loadMorePosts, loadingMore, hasMore, currentPage]", [8283, 9734], "useCallback(async (postId) => {\r\n    const commentText = newComment[postId];\r\n    if (!commentText || !commentText.trim()) {\r\n      console.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (commentText.length > 400) {\r\n      console.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await addComment(postId, commentText.trim());\r\n      if (response.success) {\r\n        // Update the post's comments count\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? { ...post, commentsCount: post.commentsCount + 1 }\r\n            : post\r\n        ));\r\n\r\n        // Add the new comment to the comments list\r\n        const newCommentObj = {\r\n          id: response.data.comment.id,\r\n          user: response.data.comment.user_name,\r\n          avatar: response.data.comment.user_avatar || DefaultProfile,\r\n          text: response.data.comment.comment,\r\n          timestamp: 'Just now',\r\n          user_id: user_id // Add user_id for permission checks\r\n        };\r\n\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\r\n        }));\r\n\r\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\r\n        console.log('Comment added successfully');\r\n      } else {\r\n        console.error('Failed to add comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n    }\r\n  })", [8283, 9734], [9767, 10860], "useCallback(async (postId, commentId) => {\r\n    const editText = editCommentText[commentId];\r\n    if (!editText || !editText.trim()) {\r\n      console.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (editText.length > 400) {\r\n      console.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await editComment(commentId, editText.trim());\r\n      if (response.success) {\r\n        // Update the comment in the comments list\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: prev[postId].map(comment =>\r\n            comment.id === commentId\r\n              ? { ...comment, text: editText.trim() }\r\n              : comment\r\n          )\r\n        }));\r\n\r\n        setEditingComment(prev => ({ ...prev, [commentId]: false }));\r\n        setEditCommentText(prev => ({ ...prev, [commentId]: '' }));\r\n        console.log('Comment updated successfully');\r\n      } else {\r\n        console.error('Failed to update comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n    }\r\n  })", [4666, 4688], "[loadingMore, hasMore, loadMorePosts]", [7171, 8607], "useCallback(async (postId) => {\r\n    const commentText = newComment[postId];\r\n    if (!commentText || !commentText.trim()) {\r\n      console.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (commentText.length > 400) {\r\n      console.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await addComment(postId, commentText.trim());\r\n      if (response.success) {\r\n        // Update the post's comments count\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? { ...post, commentsCount: post.commentsCount + 1 }\r\n            : post\r\n        ));\r\n\r\n        // Add the new comment to the comments list\r\n        const newCommentObj = {\r\n          id: response.data.comment.id,\r\n          user: response.data.comment.user_name,\r\n          user_id: response.data.comment.user_id,\r\n          avatar: response.data.comment.user_avatar || DefaultProfile,\r\n          text: response.data.comment.comment,\r\n          timestamp: 'Just now'\r\n        };\r\n\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\r\n        }));\r\n\r\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\r\n        console.log('Comment added successfully');\r\n      } else {\r\n        console.error('Failed to add comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n    }\r\n  })", [7171, 8607], [13451, 14544], [948, 956], "[fetchPostDeta<PERSON>, postId]"]