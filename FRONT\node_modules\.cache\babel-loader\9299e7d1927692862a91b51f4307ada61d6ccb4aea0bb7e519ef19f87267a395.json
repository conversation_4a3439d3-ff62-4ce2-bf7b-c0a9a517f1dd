{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\Feed.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useNavigate } from 'react-router-dom';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport FeedPost from './FeedPost.jsx';\nimport { getAllFeeds, toggleLike, addComment, getPostComments, editComment, deleteComment, generatePostShareUrl } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\n\n// Move ActionButton outside to prevent recreation on every render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ActionButton = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  icon,\n  count,\n  onClick,\n  isLiked,\n  isLast,\n  buttonStyle,\n  actionButtonStyle\n}) => {\n  _s();\n  const buttonClass = useMemo(() => `btn border ${isLiked ? 'text-danger' : 'text-muted'}`, [isLiked]);\n  const buttonStyleMemo = useMemo(() => isLast ? {\n    ...actionButtonStyle,\n    marginRight: 0\n  } : actionButtonStyle, [isLast, actionButtonStyle]);\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: buttonClass,\n    onClick: onClick,\n    style: buttonStyleMemo,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center justify-content-center\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        icon: icon,\n        style: {\n          fontSize: '1.2rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), count && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ms-1\",\n        style: {\n          fontSize: '0.9rem'\n        },\n        children: count\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n}, \"BW3KalXNlo6xULmJhXkh9aylRuM=\")), \"BW3KalXNlo6xULmJhXkh9aylRuM=\");\n_c2 = ActionButton;\nconst Feed = () => {\n  _s2();\n  const navigate = useNavigate();\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postingNewPost, setPostingNewPost] = useState(false);\n\n  // Comments state\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [editingComment, setEditingComment] = useState({}); // Track which comment is being edited\n  const [editCommentText, setEditCommentText] = useState({}); // Store edit text for each comment\n  const [userProfile, setUserProfile] = useState(null);\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\n\n  const user_id = JSON.parse(localStorage.getItem('user')).id;\n\n  // Load initial feeds\n  const loadFeeds = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) setLoading(true);else {\n        setLoadingMore(true);\n        setShowLoadingAnimation(true);\n      }\n      const response = await getAllFeeds(page, 5);\n      console.log('Get all feeds response ------------------------', response);\n      if (response.success) {\n        const newPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          comments: [],\n          // Comments will be loaded separately\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n        if (append) {\n          // Add 1 second delay for smooth loading animation\n          setTimeout(() => {\n            setPosts(prev => [...prev, ...newPosts]);\n            setLoadingMore(false);\n            setShowLoadingAnimation(false);\n          }, 1000);\n        } else {\n          setPosts(newPosts);\n          setLoading(false);\n        }\n        setHasMore(response.data.pagination.has_more);\n        setCurrentPage(page);\n\n        // Set user profile if available\n        if (response.data.user_profile) {\n          setUserProfile(response.data.user_profile);\n        }\n      } else {\n        setLoadingMore(false);\n        setShowLoadingAnimation(false);\n      }\n    } catch (error) {\n      console.error('Error loading feeds:', error);\n      setLoadingMore(false);\n      setShowLoadingAnimation(false);\n    } finally {\n      if (!append) {\n        setLoading(false);\n      }\n    }\n  }, []);\n\n  // Load more posts for infinite scroll\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', {\n        currentPage: currentPage + 1,\n        hasMore\n      });\n      loadFeeds(currentPage + 1, true);\n    }\n  }, [loadFeeds, loadingMore, hasMore, currentPage]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n\n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        console.log('Scrolled to bottom, checking if should load more...', {\n          loadingMore,\n          hasMore,\n          currentPage\n        });\n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadMorePosts, loadingMore, hasMore]);\n\n  // Initial load\n  useEffect(() => {\n    loadFeeds();\n  }, [loadFeeds]);\n\n  // Button styles\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n  const actionButtonStyle = useMemo(() => ({\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  }), [buttonStyle]);\n\n  // Event handlers\n  const handleLike = async postId => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          isLiked: response.data.is_liked,\n          likes: response.data.likes_count\n        } : post));\n      } else {\n        console.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async postId => {\n    try {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: true\n      }));\n\n      // Load all comments at once by setting a high page size\n      const response = await getPostComments(postId, 1, 1000);\n      console.log('Get post comments response ------------------------', response);\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n          }),\n          user_id: comment.user_id // Add user_id for permission checks\n        }));\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: newComments\n        }));\n      } else {\n        console.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n    } finally {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n    }\n  }, []);\n  const handleComment = postId => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({\n      ...prev,\n      [postId]: !prev[postId]\n    }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId);\n    }\n  };\n  const handleSubmitComment = async postId => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      console.error('Please enter a comment');\n      return;\n    }\n    if (commentText.length > 400) {\n      console.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount + 1\n        } : post));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now',\n          user_id: user_id // Add user_id for permission checks\n        };\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n        setNewComment(prev => ({\n          ...prev,\n          [postId]: ''\n        }));\n        console.log('Comment added successfully');\n      } else {\n        console.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n    }\n  };\n  const handleEditComment = async (postId, commentId) => {\n    const editText = editCommentText[commentId];\n    if (!editText || !editText.trim()) {\n      console.error('Please enter a comment');\n      return;\n    }\n    if (editText.length > 400) {\n      console.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await editComment(commentId, editText.trim());\n      if (response.success) {\n        // Update the comment in the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].map(comment => comment.id === commentId ? {\n            ...comment,\n            text: editText.trim()\n          } : comment)\n        }));\n        setEditingComment(prev => ({\n          ...prev,\n          [commentId]: false\n        }));\n        setEditCommentText(prev => ({\n          ...prev,\n          [commentId]: ''\n        }));\n        console.log('Comment updated successfully');\n      } else {\n        console.error('Failed to update comment');\n      }\n    } catch (error) {\n      console.error('Error updating comment:', error);\n    }\n  };\n  const handleDeleteComment = async (postId, commentId) => {\n    if (!window.confirm('Are you sure you want to delete this comment?')) {\n      return;\n    }\n    try {\n      const response = await deleteComment(commentId);\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount - 1\n        } : post));\n\n        // Remove the comment from the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].filter(comment => comment.id !== commentId)\n        }));\n        toast.success('Comment deleted successfully');\n      } else {\n        toast.error('Failed to delete comment');\n      }\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n    }\n  };\n  const handlePostSubmit = async newPost => {\n    console.log('handlePostSubmit called with:', newPost);\n\n    // Show loading state\n    setPostingNewPost(true);\n\n    // Instead of creating a fake post, let's refresh the feed to get the real data\n    setTimeout(async () => {\n      try {\n        // Refresh the feed to get the latest posts including the new one\n        await loadFeeds(1, false);\n        setPostingNewPost(false);\n      } catch (error) {\n        console.error('Error refreshing feed after post creation:', error);\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n  const handleMyFeedClick = () => {\n    navigate('/user/my-feed');\n  };\n  const handleShare = async post => {\n    try {\n      // Generate shareable URL for the post\n      const response = await generatePostShareUrl(post.id);\n      if (response.success) {\n        const shareUrl = response.data.shareUrl;\n\n        // Prepare share data\n        const shareData = {\n          title: `${post.user.name}'s Post`,\n          text: post.content || 'Check out this post!',\n          url: shareUrl\n        };\n\n        // Check if Web Share API is supported\n        if (navigator.share) {\n          await navigator.share(shareData);\n          console.log('Shared successfully');\n        } else {\n          // Fallback for browsers that don't support Web Share API\n          // Copy to clipboard\n          await navigator.clipboard.writeText(shareUrl);\n          toast.success('Post link copied to clipboard!');\n        }\n      } else {\n        toast.error('Failed to generate share link');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (!media) return null;\n    const mediaStyle = {\n      width: '100%',\n      maxHeight: '400px'\n    };\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"img\", {\n        src: media.url,\n        className: \"img-fluid rounded\",\n        alt: \"Post media\",\n        style: {\n          ...mediaStyle,\n          objectFit: 'contain'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 14\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"video\", {\n        className: \"img-fluid rounded\",\n        controls: true,\n        style: mediaStyle,\n        children: [/*#__PURE__*/_jsxDEV(\"source\", {\n          src: media.url,\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), \"Your browser does not support the video tag.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderPostContent = (content, post) => {\n    if (!content) return null;\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\n\n    // For text-only posts, show full content\n    if (!hasMedia) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text mb-2\",\n          children: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this);\n    }\n\n    // For posts with media, show truncated text with \"Show more\" option\n    const shouldTruncate = content.length > 100;\n    const isShowingFull = showFullText[post.id];\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"card-text mb-2\",\n        children: [displayText, shouldTruncate && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-link p-0 ms-2 text-primary text-decoration-none\",\n          onClick: () => setShowFullText(prev => ({\n            ...prev,\n            [post.id]: !isShowingFull\n          })),\n          children: isShowingFull ? 'Show less' : 'Show more'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Memoized comment handlers to prevent re-renders\n  const handleCommentChange = useCallback((postId, value) => {\n    setNewComment(prev => ({\n      ...prev,\n      [postId]: value\n    }));\n  }, []);\n  const handleCommentKeyDown = useCallback((e, postId) => {\n    if (e.key === 'Enter') {\n      handleSubmitComment(postId);\n    }\n  }, [handleSubmitComment]);\n  const handleCommentSubmitClick = useCallback(postId => {\n    handleSubmitComment(postId);\n  }, [handleSubmitComment]);\n  const handleEditCommentChange = useCallback((commentId, value) => {\n    setEditCommentText(prev => ({\n      ...prev,\n      [commentId]: value\n    }));\n  }, []);\n  const handleEditCommentKeyDown = useCallback((e, postId, commentId) => {\n    if (e.key === 'Enter') {\n      handleEditComment(postId, commentId);\n    }\n  }, [handleEditComment]);\n  const renderComments = post => {\n    if (!showComments[post.id]) return null;\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-top pt-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-3\",\n        children: [\"Comments (\", post.commentsCount || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: DefaultProfile,\n          className: \"rounded-circle me-2\",\n          alt: \"Profile\",\n          style: {\n            width: '32px',\n            height: '32px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Write a comment...\",\n            value: newComment[post.id] || '',\n            onChange: e => handleCommentChange(post.id, e.target.value),\n            onKeyDown: e => handleCommentKeyDown(e, post.id),\n            maxLength: 400\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end mt-1\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: (newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n              children: [(newComment[post.id] || '').length, \"/400 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm ms-2 w-auto\",\n          onClick: () => handleCommentSubmitClick(post.id),\n          disabled: !newComment[post.id] || !newComment[post.id].trim(),\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border spinner-border-sm\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading comments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted small\",\n          children: \"Loading comments...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          id: `comments-container-${post.id}`,\n          children: comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: comment.avatar,\n              className: \"rounded-circle me-2\",\n              alt: comment.user,\n              style: {\n                width: '32px',\n                height: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-light rounded p-2 flex-grow-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold\",\n                  children: comment.user\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 23\n                }, this), comment.user_id === user_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-1\",\n                  children: editingComment[comment.id] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-success\",\n                      onClick: () => handleEditComment(post.id, comment.id),\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:check\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 571,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-secondary\",\n                      onClick: () => {\n                        setEditingComment(prev => ({\n                          ...prev,\n                          [comment.id]: false\n                        }));\n                        setEditCommentText(prev => ({\n                          ...prev,\n                          [comment.id]: ''\n                        }));\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:close\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 580,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-primary\",\n                      onClick: () => {\n                        setEditingComment(prev => ({\n                          ...prev,\n                          [comment.id]: true\n                        }));\n                        setEditCommentText(prev => ({\n                          ...prev,\n                          [comment.id]: comment.text\n                        }));\n                      },\n                      title: \"Edit comment\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:pencil\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-danger\",\n                      onClick: () => handleDeleteComment(post.id, comment.id),\n                      title: \"Delete comment\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:delete\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 21\n              }, this), editingComment[comment.id] ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control form-control-sm\",\n                  value: editCommentText[comment.id] || '',\n                  onChange: e => handleEditCommentChange(comment.id, e.target.value),\n                  onKeyDown: e => handleEditCommentKeyDown(e, post.id, comment.id),\n                  maxLength: 400,\n                  autoFocus: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-end mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: (editCommentText[comment.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n                    children: [(editCommentText[comment.id] || '').length, \"/400 characters\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  wordWrap: 'break-word',\n                  wordBreak: 'break-word',\n                  overflowWrap: 'break-word',\n                  whiteSpace: 'pre-wrap',\n                  maxWidth: '100%'\n                },\n                children: comment.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-muted small mt-1\",\n                children: comment.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this)]\n          }, comment.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes fadeIn {\n            from {\n              opacity: 0;\n              transform: translateY(-10px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          @keyframes slideInDown {\n            from {\n              opacity: 0;\n              transform: translateY(-30px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n          \n          .card {\n            transition: all 0.3s ease-in-out;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            onClick: handleMyFeedClick,\n            style: {\n              cursor: 'pointer',\n              padding: '8px',\n              borderRadius: '8px',\n              transition: 'background-color 0.2s ease'\n            },\n            onMouseEnter: e => e.currentTarget.style.backgroundColor = '#f8f9fa',\n            onMouseLeave: e => e.currentTarget.style.backgroundColor = 'transparent',\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-end me-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Click to view your feed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n              className: \"rounded-circle\",\n              alt: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"User Profile\",\n              style: {\n                width: '50px',\n                height: '50px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeedPost, {\n          onPostSubmit: handlePostSubmit,\n          userProfile: userProfile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), postingNewPost && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          style: {\n            animation: 'fadeIn 0.5s ease-in-out',\n            border: '2px dashed #007bff',\n            backgroundColor: '#f8f9fa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-3\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Creating post...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-2\",\n              children: \"Creating your post...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Please wait while we process your content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"Loading posts...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 13\n        }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:post-outline\",\n            style: {\n              fontSize: '3rem',\n              color: '#6c757d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"No posts yet. Be the first to share something!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [posts.map((post, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card mb-4\",\n            style: {\n              animation: index === 0 && !postingNewPost ? 'slideInDown 0.6s ease-out' : 'none',\n              transform: index === 0 && !postingNewPost ? 'translateY(0)' : 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: post.user.avatar,\n                  className: \"rounded-circle me-3\",\n                  alt: post.user.name,\n                  style: {\n                    width: '40px',\n                    height: '40px',\n                    objectFit: 'contain'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: post.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: new Date(post.created_at).toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric'\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [renderPostContent(post.content, post), renderMedia(post.media)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\",\n                  count: post.likes,\n                  onClick: () => handleLike(post.id),\n                  isLiked: post.isLiked,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:comment-outline\",\n                  count: post.commentsCount || 0,\n                  onClick: () => handleComment(post.id),\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:share-variant-outline\",\n                  onClick: () => handleShare(post),\n                  isLast: true,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 21\n              }, this), renderComments(post)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 19\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 17\n          }, this)), showLoadingAnimation && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            style: {\n              animation: 'fadeIn 0.5s ease-in-out'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more posts...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-primary mb-0\",\n              children: \"Loading more posts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 17\n          }, this), !showLoadingAnimation && !loadingMore && hasMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline-primary\",\n              onClick: loadMorePosts,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"mdi:chevron-down\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 21\n              }, this), \"Load More Posts\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 17\n          }, this), !hasMore && posts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"You've reached the end of the feed!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 653,\n    columnNumber: 5\n  }, this);\n};\n_s2(Feed, \"1lKo7sZfWgpse3Ln8/Fzxgdwupc=\", false, function () {\n  return [useNavigate];\n});\n_c3 = Feed;\nexport default Feed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ActionButton$React.memo\");\n$RefreshReg$(_c2, \"ActionButton\");\n$RefreshReg$(_c3, \"Feed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Icon", "useNavigate", "DefaultProfile", "FeedPost", "getAllFeeds", "toggleLike", "addComment", "getPostComments", "editComment", "deleteComment", "generatePostShareUrl", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ActionButton", "_s", "memo", "_c", "icon", "count", "onClick", "isLiked", "isLast", "buttonStyle", "actionButtonStyle", "buttonClass", "buttonStyleMemo", "marginRight", "className", "style", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "Feed", "_s2", "navigate", "posts", "setPosts", "loading", "setLoading", "loadingMore", "setLoadingMore", "currentPage", "setCurrentPage", "hasMore", "setHasMore", "showLoadingAnimation", "setShowLoadingAnimation", "newComment", "setNewComment", "showComments", "setShowComments", "postingNewPost", "setPostingNewPost", "postComments", "setPostComments", "commentsLoading", "setCommentsLoading", "editingComment", "setEditingComment", "editCommentText", "setEditCommentText", "userProfile", "setUserProfile", "showFullText", "setShowFullText", "user_id", "JSON", "parse", "localStorage", "getItem", "id", "loadFeeds", "page", "append", "response", "console", "log", "success", "newPosts", "data", "map", "post", "user", "name", "user_name", "avatar", "user_avatar", "content", "description", "media", "media_url", "type", "media_type", "url", "is_liked_by_user", "likes", "likes_count", "comments", "commentsCount", "comments_count", "created_at", "setTimeout", "prev", "pagination", "has_more", "user_profile", "error", "loadMorePosts", "handleScroll", "scrollTop", "document", "documentElement", "scrollHeight", "clientHeight", "window", "addEventListener", "removeEventListener", "backgroundColor", "borderColor", "flex", "handleLike", "postId", "is_liked", "loadPostComments", "newComments", "comment", "text", "timestamp", "Date", "commented_at", "toLocaleDateString", "year", "month", "day", "handleComment", "isOpening", "handleSubmitComment", "commentText", "trim", "length", "newCommentObj", "handleEditComment", "commentId", "editText", "handleDeleteComment", "confirm", "filter", "handlePostSubmit", "newPost", "handleMyFeedClick", "handleShare", "shareUrl", "shareData", "title", "navigator", "share", "clipboard", "writeText", "renderMedia", "mediaStyle", "width", "maxHeight", "src", "alt", "objectFit", "controls", "renderPostContent", "hasMedia", "shouldTruncate", "isShowingFull", "displayText", "substring", "handleCommentChange", "value", "handleCommentKeyDown", "e", "key", "handleCommentSubmitClick", "handleEditCommentChange", "handleEditCommentKeyDown", "renderComments", "isLoading", "height", "placeholder", "onChange", "target", "onKeyDown", "max<PERSON><PERSON><PERSON>", "disabled", "role", "autoFocus", "wordWrap", "wordBreak", "overflowWrap", "whiteSpace", "max<PERSON><PERSON><PERSON>", "cursor", "padding", "borderRadius", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "profile_pic_url", "onPostSubmit", "animation", "border", "color", "index", "transform", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/Feed.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react'\r\nimport { Icon } from '@iconify/react'\r\nimport { useNavigate } from 'react-router-dom'\r\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\r\nimport FeedPost from './FeedPost.jsx'\r\nimport { getAllFeeds, toggleLike, addComment, getPostComments, editComment, deleteComment, generatePostShareUrl } from '../../../services/feedServices'\r\nimport { toast } from 'react-toastify'\r\n\r\n// Move ActionButton outside to prevent recreation on every render\r\nconst ActionButton = React.memo(({ icon, count, onClick, isLiked, isLast, buttonStyle, actionButtonStyle }) => {\r\n  const buttonClass = useMemo(() =>\r\n    `btn border ${isLiked ? 'text-danger' : 'text-muted'}`,\r\n    [isLiked]\r\n  );\r\n\r\n  const buttonStyleMemo = useMemo(() =>\r\n    isLast ? { ...actionButtonStyle, marginRight: 0 } : actionButtonStyle,\r\n    [isLast, actionButtonStyle]\r\n  );\r\n\r\n  return (\r\n    <button\r\n      className={buttonClass}\r\n      onClick={onClick}\r\n      style={buttonStyleMemo}\r\n    >\r\n      <div className=\"d-flex align-items-center justify-content-center\">\r\n        <Icon icon={icon} style={{fontSize: '1.2rem'}} />\r\n        {count && <span className=\"ms-1\" style={{fontSize: '0.9rem'}}>{count}</span>}\r\n      </div>\r\n    </button>\r\n  );\r\n});\r\n\r\nconst Feed = () => {\r\n  const navigate = useNavigate();\r\n\r\n  const [posts, setPosts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [loadingMore, setLoadingMore] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [hasMore, setHasMore] = useState(true);\r\n  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);\r\n  const [newComment, setNewComment] = useState({});\r\n  const [showComments, setShowComments] = useState({});\r\n  const [postingNewPost, setPostingNewPost] = useState(false);\r\n\r\n  // Comments state\r\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\r\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\r\n  const [editingComment, setEditingComment] = useState({}); // Track which comment is being edited\r\n  const [editCommentText, setEditCommentText] = useState({}); // Store edit text for each comment\r\n  const [userProfile, setUserProfile] = useState(null);\r\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\r\n\r\n  const user_id = JSON.parse(localStorage.getItem('user')).id;\r\n\r\n  // Load initial feeds\r\n  const loadFeeds = useCallback(async (page = 1, append = false) => {\r\n    try {\r\n      if (page === 1) setLoading(true);\r\n      else {\r\n        setLoadingMore(true);\r\n        setShowLoadingAnimation(true);\r\n      }\r\n\r\n      const response = await getAllFeeds(page, 5);\r\n      console.log('Get all feeds response ------------------------', response);\r\n\r\n      if (response.success) {\r\n        const newPosts = response.data.posts.map(post => ({\r\n          id: post.id,\r\n          user: {\r\n            name: post.user_name,\r\n            avatar: post.user_avatar || DefaultProfile\r\n          },\r\n          content: post.description,\r\n          media: post.media_url ? {\r\n            type: post.media_type,\r\n            url: post.media_url\r\n          } : null,\r\n          isLiked: post.is_liked_by_user === 1,\r\n          likes: post.likes_count,\r\n          comments: [], // Comments will be loaded separately\r\n          commentsCount: post.comments_count,\r\n          created_at: post.created_at\r\n        }));\r\n\r\n        if (append) {\r\n          // Add 1 second delay for smooth loading animation\r\n          setTimeout(() => {\r\n            setPosts(prev => [...prev, ...newPosts]);\r\n            setLoadingMore(false);\r\n            setShowLoadingAnimation(false);\r\n          }, 1000);\r\n        } else {\r\n          setPosts(newPosts);\r\n          setLoading(false);\r\n        }\r\n\r\n        setHasMore(response.data.pagination.has_more);\r\n        setCurrentPage(page);\r\n        \r\n        // Set user profile if available\r\n        if (response.data.user_profile) {\r\n          setUserProfile(response.data.user_profile);\r\n        }\r\n      } else {\r\n        setLoadingMore(false);\r\n        setShowLoadingAnimation(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading feeds:', error);\r\n      setLoadingMore(false);\r\n      setShowLoadingAnimation(false);\r\n    } finally {\r\n      if (!append) {\r\n        setLoading(false);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Load more posts for infinite scroll\r\n  const loadMorePosts = useCallback(() => {\r\n    if (!loadingMore && hasMore) {\r\n      console.log('Loading more posts...', { currentPage: currentPage + 1, hasMore });\r\n      loadFeeds(currentPage + 1, true);\r\n    }\r\n  }, [loadFeeds, loadingMore, hasMore, currentPage]);\r\n\r\n  // Infinite scroll handler\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const scrollTop = document.documentElement.scrollTop;\r\n      const scrollHeight = document.documentElement.scrollHeight;\r\n      const clientHeight = document.documentElement.clientHeight;\r\n      \r\n      // Check if user has scrolled to bottom (with 100px threshold)\r\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\r\n        console.log('Scrolled to bottom, checking if should load more...', {\r\n          loadingMore,\r\n          hasMore,\r\n          currentPage\r\n        });\r\n        \r\n        if (!loadingMore && hasMore) {\r\n          loadMorePosts();\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [loadMorePosts, loadingMore, hasMore]);\r\n\r\n  // Initial load\r\n  useEffect(() => {\r\n    loadFeeds();\r\n  }, [loadFeeds]);\r\n\r\n  // Button styles\r\n  // Memoized styles to prevent re-renders\r\n  const buttonStyle = useMemo(() => ({\r\n    backgroundColor: 'transparent',\r\n    borderColor: '#dee2e6'\r\n  }), []);\r\n\r\n  const actionButtonStyle = useMemo(() => ({\r\n    flex: 1,\r\n    marginRight: '10px',\r\n    ...buttonStyle\r\n  }), [buttonStyle]);\r\n\r\n  // Event handlers\r\n  const handleLike = async (postId) => {\r\n    try {\r\n      const response = await toggleLike(postId);\r\n      if (response.success) {\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? {\r\n                ...post,\r\n                isLiked: response.data.is_liked,\r\n                likes: response.data.likes_count\r\n              }\r\n            : post\r\n        ));\r\n      } else {\r\n        console.error('Failed to update like');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error toggling like:', error);\r\n    }\r\n  };\r\n\r\n  // Load comments for a specific post\r\n  const loadPostComments = useCallback(async (postId) => {\r\n    try {\r\n      setCommentsLoading(prev => ({ ...prev, [postId]: true }));\r\n\r\n      // Load all comments at once by setting a high page size\r\n      const response = await getPostComments(postId, 1, 1000);\r\n\r\n      console.log('Get post comments response ------------------------', response);\r\n\r\n      if (response.success) {\r\n        const newComments = response.data.comments.map(comment => ({\r\n          id: comment.id,\r\n          user: comment.user_name,\r\n          avatar: comment.user_avatar || DefaultProfile,\r\n          text: comment.comment,\r\n          timestamp: new Date(comment.commented_at).toLocaleDateString('en-US', {\r\n            year: 'numeric',\r\n            month: 'long',\r\n            day: 'numeric'\r\n          }),\r\n          user_id: comment.user_id // Add user_id for permission checks\r\n        }));\r\n\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: newComments\r\n        }));\r\n      } else {\r\n          console.error('Failed to load comments');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading comments:', error);\r\n    } finally {\r\n      setCommentsLoading(prev => ({ ...prev, [postId]: false }));\r\n    }\r\n  }, []);\r\n\r\n  const handleComment = (postId) => {\r\n    const isOpening = !showComments[postId];\r\n    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));\r\n\r\n    // Load comments when opening comments section for the first time\r\n    if (isOpening && !postComments[postId]) {\r\n      loadPostComments(postId);\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n  const handleSubmitComment = async (postId) => {\r\n    const commentText = newComment[postId];\r\n    if (!commentText || !commentText.trim()) {\r\n      console.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (commentText.length > 400) {\r\n      console.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await addComment(postId, commentText.trim());\r\n      if (response.success) {\r\n        // Update the post's comments count\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? { ...post, commentsCount: post.commentsCount + 1 }\r\n            : post\r\n        ));\r\n\r\n        // Add the new comment to the comments list\r\n        const newCommentObj = {\r\n          id: response.data.comment.id,\r\n          user: response.data.comment.user_name,\r\n          avatar: response.data.comment.user_avatar || DefaultProfile,\r\n          text: response.data.comment.comment,\r\n          timestamp: 'Just now',\r\n          user_id: user_id // Add user_id for permission checks\r\n        };\r\n\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\r\n        }));\r\n\r\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\r\n        console.log('Comment added successfully');\r\n      } else {\r\n        console.error('Failed to add comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n    }\r\n  };\r\n\r\n  const handleEditComment = async (postId, commentId) => {\r\n    const editText = editCommentText[commentId];\r\n    if (!editText || !editText.trim()) {\r\n      console.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (editText.length > 400) {\r\n      console.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await editComment(commentId, editText.trim());\r\n      if (response.success) {\r\n        // Update the comment in the comments list\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: prev[postId].map(comment =>\r\n            comment.id === commentId\r\n              ? { ...comment, text: editText.trim() }\r\n              : comment\r\n          )\r\n        }));\r\n\r\n        setEditingComment(prev => ({ ...prev, [commentId]: false }));\r\n        setEditCommentText(prev => ({ ...prev, [commentId]: '' }));\r\n        console.log('Comment updated successfully');\r\n      } else {\r\n        console.error('Failed to update comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n    }\r\n  };\r\n\r\n  const handleDeleteComment = async (postId, commentId) => {\r\n    if (!window.confirm('Are you sure you want to delete this comment?')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await deleteComment(commentId);\r\n      if (response.success) {\r\n        // Update the post's comments count\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? { ...post, commentsCount: post.commentsCount - 1 }\r\n            : post\r\n        ));\r\n\r\n        // Remove the comment from the comments list\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: prev[postId].filter(comment => comment.id !== commentId)\r\n        }));\r\n\r\n        toast.success('Comment deleted successfully');\r\n      } else {\r\n        toast.error('Failed to delete comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting comment:', error);\r\n      toast.error('Failed to delete comment');\r\n    }\r\n  };\r\n\r\n  const handlePostSubmit = async (newPost) => {\r\n    console.log('handlePostSubmit called with:', newPost);\r\n    \r\n    // Show loading state\r\n    setPostingNewPost(true);\r\n    \r\n    // Instead of creating a fake post, let's refresh the feed to get the real data\r\n    setTimeout(async () => {\r\n      try {\r\n        // Refresh the feed to get the latest posts including the new one\r\n        await loadFeeds(1, false);\r\n        setPostingNewPost(false);\r\n      } catch (error) {\r\n        console.error('Error refreshing feed after post creation:', error);\r\n        setPostingNewPost(false);\r\n      }\r\n    }, 2000); // 2 second delay\r\n  };\r\n\r\n\r\n\r\n  const handleMyFeedClick = () => {\r\n    navigate('/user/my-feed');\r\n  };\r\n\r\n  const handleShare = async (post) => {\r\n    try {\r\n      // Generate shareable URL for the post\r\n      const response = await generatePostShareUrl(post.id);\r\n\r\n      if (response.success) {\r\n        const shareUrl = response.data.shareUrl;\r\n\r\n        // Prepare share data\r\n        const shareData = {\r\n          title: `${post.user.name}'s Post`,\r\n          text: post.content || 'Check out this post!',\r\n          url: shareUrl\r\n        };\r\n\r\n        // Check if Web Share API is supported\r\n        if (navigator.share) {\r\n          await navigator.share(shareData);\r\n          console.log('Shared successfully');\r\n        } else {\r\n          // Fallback for browsers that don't support Web Share API\r\n          // Copy to clipboard\r\n          await navigator.clipboard.writeText(shareUrl);\r\n          toast.success('Post link copied to clipboard!');\r\n        }\r\n      } else {\r\n        toast.error('Failed to generate share link');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error sharing post:', error);\r\n      if (error.name !== 'AbortError') {\r\n        toast.error('Failed to share post');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Render functions\r\n  const renderMedia = (media) => {\r\n    if (!media) return null;\r\n\r\n    const mediaStyle = { width: '100%', maxHeight: '400px' };\r\n\r\n    if (media.type === 'image') {\r\n      return <img src={media.url} className=\"img-fluid rounded\" alt=\"Post media\" style={{...mediaStyle, objectFit: 'contain'}} />;\r\n    } else if (media.type === 'video') {\r\n      return (\r\n        <video className=\"img-fluid rounded\" controls style={mediaStyle}>\r\n          <source src={media.url} type=\"video/mp4\" />\r\n          Your browser does not support the video tag.\r\n        </video>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const renderPostContent = (content, post) => {\r\n    if (!content) return null;\r\n\r\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\r\n\r\n    // For text-only posts, show full content\r\n    if (!hasMedia) {\r\n      return (\r\n        <div>\r\n          <p className=\"card-text mb-2\">{content}</p>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // For posts with media, show truncated text with \"Show more\" option\r\n    const shouldTruncate = content.length > 100;\r\n    const isShowingFull = showFullText[post.id];\r\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\r\n\r\n    return (\r\n      <div>\r\n        <p className=\"card-text mb-2\">\r\n          {displayText}\r\n          {shouldTruncate && (\r\n            <button\r\n              className=\"btn btn-link p-0 ms-2 text-primary text-decoration-none\"\r\n              onClick={() => setShowFullText(prev => ({ ...prev, [post.id]: !isShowingFull }))}\r\n            >\r\n              {isShowingFull ? 'Show less' : 'Show more'}\r\n            </button>\r\n          )}\r\n        </p>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Memoized comment handlers to prevent re-renders\r\n  const handleCommentChange = useCallback((postId, value) => {\r\n    setNewComment(prev => ({ ...prev, [postId]: value }));\r\n  }, []);\r\n\r\n  const handleCommentKeyDown = useCallback((e, postId) => {\r\n    if (e.key === 'Enter') {\r\n      handleSubmitComment(postId);\r\n    }\r\n  }, [handleSubmitComment]);\r\n\r\n  const handleCommentSubmitClick = useCallback((postId) => {\r\n    handleSubmitComment(postId);\r\n  }, [handleSubmitComment]);\r\n\r\n  const handleEditCommentChange = useCallback((commentId, value) => {\r\n    setEditCommentText(prev => ({ ...prev, [commentId]: value }));\r\n  }, []);\r\n\r\n  const handleEditCommentKeyDown = useCallback((e, postId, commentId) => {\r\n    if (e.key === 'Enter') {\r\n      handleEditComment(postId, commentId);\r\n    }\r\n  }, [handleEditComment]);\r\n\r\n  const renderComments = (post) => {\r\n    if (!showComments[post.id]) return null;\r\n\r\n    const comments = postComments[post.id] || [];\r\n    const isLoading = commentsLoading[post.id];\r\n\r\n    return (\r\n      <div className=\"border-top pt-3 mt-3\">\r\n        <h6 className=\"mb-3\">Comments ({post.commentsCount || 0})</h6>\r\n\r\n        {/* Comment Input */}\r\n        <div className=\"d-flex mb-3\">\r\n          <img src={DefaultProfile} className=\"rounded-circle me-2\" alt=\"Profile\" style={{width: '32px', height: '32px'}} />\r\n          <div className=\"flex-grow-1\">\r\n            <input\r\n              type=\"text\"\r\n              className=\"form-control\"\r\n              placeholder=\"Write a comment...\"\r\n              value={newComment[post.id] || ''}\r\n              onChange={(e) => handleCommentChange(post.id, e.target.value)}\r\n              onKeyDown={(e) => handleCommentKeyDown(e, post.id)}\r\n              maxLength={400}\r\n            />\r\n            <div className=\"d-flex justify-content-end mt-1\">\r\n              <small className={(newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\r\n                {(newComment[post.id] || '').length}/400 characters\r\n              </small>\r\n            </div>\r\n          </div>\r\n          <button\r\n            className=\"btn btn-primary btn-sm ms-2 w-auto\"\r\n            onClick={() => handleCommentSubmitClick(post.id)}\r\n            disabled={!newComment[post.id] || !newComment[post.id].trim()}\r\n          >\r\n            <Icon icon=\"mdi:send\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Comments Loading State */}\r\n        {isLoading ? (\r\n          <div className=\"text-center py-3\">\r\n            <div className=\"spinner-border spinner-border-sm\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading comments...</span>\r\n            </div>\r\n            <p className=\"mt-2 text-muted small\">Loading comments...</p>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Comments Container */}\r\n            <div\r\n              id={`comments-container-${post.id}`}\r\n            >\r\n              {/* Existing Comments */}\r\n              {comments.map(comment => (\r\n                <div key={comment.id} className=\"d-flex mb-2\">\r\n                  <img src={comment.avatar} className=\"rounded-circle me-2\" alt={comment.user} style={{width: '32px', height: '32px'}} />\r\n                  <div className=\"bg-light rounded p-2 flex-grow-1\">\r\n                    <div className=\"d-flex justify-content-between align-items-start\">\r\n                      <div className=\"fw-bold\">{comment.user}</div>\r\n                      {/* Show edit/delete options only for user's own comments */}\r\n                      {comment.user_id === user_id && (\r\n                        <div className=\"d-flex gap-1\">\r\n                          {editingComment[comment.id] ? (\r\n                            <>\r\n                              <button\r\n                                className=\"btn btn-sm btn-success\"\r\n                                onClick={() => handleEditComment(post.id, comment.id)}\r\n                              >\r\n                                <Icon icon=\"mdi:check\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                              <button\r\n                                className=\"btn btn-sm btn-secondary\"\r\n                                onClick={() => {\r\n                                  setEditingComment(prev => ({ ...prev, [comment.id]: false }));\r\n                                  setEditCommentText(prev => ({ ...prev, [comment.id]: '' }));\r\n                                }}\r\n                              >\r\n                                <Icon icon=\"mdi:close\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <button\r\n                                className=\"btn btn-sm btn-outline-primary\"\r\n                                onClick={() => {\r\n                                  setEditingComment(prev => ({ ...prev, [comment.id]: true }));\r\n                                  setEditCommentText(prev => ({ ...prev, [comment.id]: comment.text }));\r\n                                }}\r\n                                title=\"Edit comment\"\r\n                              >\r\n                                <Icon icon=\"mdi:pencil\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                              <button\r\n                                className=\"btn btn-sm btn-outline-danger\"\r\n                                onClick={() => handleDeleteComment(post.id, comment.id)}\r\n                                title=\"Delete comment\"\r\n                              >\r\n                                <Icon icon=\"mdi:delete\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                            </>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    \r\n                    {editingComment[comment.id] ? (\r\n                      <div className=\"mt-2\">\r\n                        <input\r\n                          type=\"text\"\r\n                          className=\"form-control form-control-sm\"\r\n                          value={editCommentText[comment.id] || ''}\r\n                          onChange={(e) => handleEditCommentChange(comment.id, e.target.value)}\r\n                          onKeyDown={(e) => handleEditCommentKeyDown(e, post.id, comment.id)}\r\n                          maxLength={400}\r\n                          autoFocus\r\n                        />\r\n                        <div className=\"d-flex justify-content-end mt-1\">\r\n                          <small className={(editCommentText[comment.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\r\n                            {(editCommentText[comment.id] || '').length}/400 characters\r\n                          </small>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div style={{\r\n                        wordWrap: 'break-word',\r\n                        wordBreak: 'break-word',\r\n                        overflowWrap: 'break-word',\r\n                        whiteSpace: 'pre-wrap',\r\n                        maxWidth: '100%'\r\n                      }}>\r\n                        {comment.text}\r\n                      </div>\r\n                    )}\r\n                    \r\n                    <div className=\"text-muted small mt-1\">{comment.timestamp}</div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n\r\n\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"container py-4\">\r\n      <style>\r\n        {`\r\n          @keyframes fadeIn {\r\n            from {\r\n              opacity: 0;\r\n              transform: translateY(-10px);\r\n            }\r\n            to {\r\n              opacity: 1;\r\n              transform: translateY(0);\r\n            }\r\n          }\r\n          \r\n          @keyframes slideInDown {\r\n            from {\r\n              opacity: 0;\r\n              transform: translateY(-30px);\r\n            }\r\n            to {\r\n              opacity: 1;\r\n              transform: translateY(0);\r\n            }\r\n          }\r\n          \r\n          .card {\r\n            transition: all 0.3s ease-in-out;\r\n          }\r\n        `}\r\n      </style>\r\n      <div className=\"row justify-content-center\">\r\n        <div className=\"col-md-8\">\r\n          {/* Profile Header */}\r\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n            <div></div>\r\n            <div \r\n              className=\"d-flex align-items-center\"\r\n              onClick={handleMyFeedClick}\r\n              style={{ \r\n                cursor: 'pointer',\r\n                padding: '8px',\r\n                borderRadius: '8px',\r\n                transition: 'background-color 0.2s ease'\r\n              }}\r\n              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}\r\n              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}\r\n            >\r\n              <div className=\"text-end me-3\">\r\n                <h5 className=\"mb-0\">{userProfile?.name || 'User'}</h5>\r\n                <small className=\"text-muted\">Click to view your feed</small>\r\n              </div>\r\n              <img \r\n                src={userProfile?.profile_pic_url || DefaultProfile} \r\n                className=\"rounded-circle\" \r\n                alt={userProfile?.name || \"User Profile\"} \r\n                style={{width: '50px', height: '50px'}} \r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Create Post Component */}\r\n          <FeedPost onPostSubmit={handlePostSubmit} userProfile={userProfile} />\r\n\r\n          {/* New Post Loading State */}\r\n          {postingNewPost && (\r\n            <div className=\"card mb-4\" style={{\r\n              animation: 'fadeIn 0.5s ease-in-out',\r\n              border: '2px dashed #007bff',\r\n              backgroundColor: '#f8f9fa'\r\n            }}>\r\n              <div className=\"card-body text-center py-4\">\r\n                <div className=\"spinner-border text-primary mb-3\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Creating post...</span>\r\n                </div>\r\n                <h6 className=\"text-primary mb-2\">Creating your post...</h6>\r\n                <p className=\"text-muted mb-0\">Please wait while we process your content</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Loading State */}\r\n          {loading ? (\r\n            <div className=\"text-center py-4\">\r\n              <div className=\"spinner-border\" role=\"status\">\r\n                <span className=\"visually-hidden\">Loading...</span>\r\n              </div>\r\n              <p className=\"mt-2 text-muted\">Loading posts...</p>\r\n            </div>\r\n          ) : posts.length === 0 ? (\r\n            <div className=\"text-center py-4\">\r\n              <Icon icon=\"mdi:post-outline\" style={{ fontSize: '3rem', color: '#6c757d' }} />\r\n              <p className=\"mt-2 text-muted\">No posts yet. Be the first to share something!</p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Posts Feed */}\r\n              {posts.map((post, index) => (\r\n                <div \r\n                  key={post.id} \r\n                  className=\"card mb-4\"\r\n                  style={{\r\n                    animation: index === 0 && !postingNewPost ? 'slideInDown 0.6s ease-out' : 'none',\r\n                    transform: index === 0 && !postingNewPost ? 'translateY(0)' : 'none'\r\n                  }}\r\n                >\r\n                  <div className=\"card-body\">\r\n                    {/* Post Header */}\r\n                    <div className=\"d-flex align-items-center mb-3\">\r\n                      <img src={post.user.avatar} className=\"rounded-circle me-3\" alt={post.user.name} style={{width: '40px', height: '40px',objectFit:'contain'}} />\r\n                      <div className=\"flex-grow-1\">\r\n                        <h6 className=\"mb-0\">{post.user.name}</h6>\r\n                        <small className=\"text-muted\">{new Date(post.created_at).toLocaleDateString('en-US', {\r\n                        year: 'numeric',\r\n                        month: 'long',\r\n                        day: 'numeric'\r\n                      })}</small>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Post Content */}\r\n                    <div className=\"mb-3\">\r\n                      {renderPostContent(post.content, post)}\r\n                      {renderMedia(post.media)}\r\n                    </div>\r\n\r\n                    {/* Action Buttons */}\r\n                    <div className=\"d-flex justify-content-between\">\r\n                      <ActionButton\r\n                        icon={post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\"}\r\n                        count={post.likes}\r\n                        onClick={() => handleLike(post.id)}\r\n                        isLiked={post.isLiked}\r\n                        buttonStyle={buttonStyle}\r\n                        actionButtonStyle={actionButtonStyle}\r\n                      />\r\n                      <ActionButton\r\n                        icon=\"mdi:comment-outline\"\r\n                        count={post.commentsCount || 0}\r\n                        onClick={() => handleComment(post.id)}\r\n                        buttonStyle={buttonStyle}\r\n                        actionButtonStyle={actionButtonStyle}\r\n                      />\r\n                      <ActionButton\r\n                        icon=\"mdi:share-variant-outline\"\r\n                        onClick={() => handleShare(post)}\r\n                        isLast={true}\r\n                        buttonStyle={buttonStyle}\r\n                        actionButtonStyle={actionButtonStyle}\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Comments Section */}\r\n                    {renderComments(post)}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n\r\n              {/* Load More Button */}\r\n              {showLoadingAnimation && (\r\n                <div className=\"text-center py-4\" style={{\r\n                  animation: 'fadeIn 0.5s ease-in-out'\r\n                }}>\r\n                  <div className=\"spinner-border text-primary mb-2\" role=\"status\">\r\n                    <span className=\"visually-hidden\">Loading more posts...</span>\r\n                  </div>\r\n                  <p className=\"text-primary mb-0\">Loading more posts...</p>\r\n                </div>\r\n              )}\r\n\r\n              {!showLoadingAnimation && !loadingMore && hasMore && (\r\n                <div className=\"text-center py-3\">\r\n                  <button \r\n                    className=\"btn btn-outline-primary\"\r\n                    onClick={loadMorePosts}\r\n                  >\r\n                    <Icon icon=\"mdi:chevron-down\" className=\"me-2\" />\r\n                    Load More Posts\r\n                  </button>\r\n                </div>\r\n              )}\r\n\r\n              {!hasMore && posts.length > 0 && (\r\n                <div className=\"text-center py-3\">\r\n                  <p className=\"text-muted\">You've reached the end of the feed!</p>\r\n                </div>\r\n              )}\r\n\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Feed;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,WAAW,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,gCAAgC;AACvJ,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,gBAAAC,EAAA,cAAGtB,KAAK,CAACuB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC,WAAW;EAAEC;AAAkB,CAAC,KAAK;EAAAT,EAAA;EAC7G,MAAMU,WAAW,GAAG5B,OAAO,CAAC,MAC1B,cAAcwB,OAAO,GAAG,aAAa,GAAG,YAAY,EAAE,EACtD,CAACA,OAAO,CACV,CAAC;EAED,MAAMK,eAAe,GAAG7B,OAAO,CAAC,MAC9ByB,MAAM,GAAG;IAAE,GAAGE,iBAAiB;IAAEG,WAAW,EAAE;EAAE,CAAC,GAAGH,iBAAiB,EACrE,CAACF,MAAM,EAAEE,iBAAiB,CAC5B,CAAC;EAED,oBACEb,OAAA;IACEiB,SAAS,EAAEH,WAAY;IACvBL,OAAO,EAAEA,OAAQ;IACjBS,KAAK,EAAEH,eAAgB;IAAAI,QAAA,eAEvBnB,OAAA;MAAKiB,SAAS,EAAC,kDAAkD;MAAAE,QAAA,gBAC/DnB,OAAA,CAACb,IAAI;QAACoB,IAAI,EAAEA,IAAK;QAACW,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChDhB,KAAK,iBAAIR,OAAA;QAAMiB,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ,CAAE;QAAAD,QAAA,EAAEX;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC,kCAAC;AAACC,GAAA,GAvBGtB,YAAY;AAyBlB,MAAMuB,IAAI,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjB,MAAMC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACoE,cAAc,EAAEC,iBAAiB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtD,MAAM4E,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE;;EAE3D;EACA,MAAMC,SAAS,GAAGhF,WAAW,CAAC,OAAOiF,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAChE,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAElC,UAAU,CAAC,IAAI,CAAC,CAAC,KAC5B;QACHE,cAAc,CAAC,IAAI,CAAC;QACpBM,uBAAuB,CAAC,IAAI,CAAC;MAC/B;MAEA,MAAM4B,QAAQ,GAAG,MAAM7E,WAAW,CAAC2E,IAAI,EAAE,CAAC,CAAC;MAC3CG,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEF,QAAQ,CAAC;MAExE,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAMC,QAAQ,GAAGJ,QAAQ,CAACK,IAAI,CAAC5C,KAAK,CAAC6C,GAAG,CAACC,IAAI,KAAK;UAChDX,EAAE,EAAEW,IAAI,CAACX,EAAE;UACXY,IAAI,EAAE;YACJC,IAAI,EAAEF,IAAI,CAACG,SAAS;YACpBC,MAAM,EAAEJ,IAAI,CAACK,WAAW,IAAI3F;UAC9B,CAAC;UACD4F,OAAO,EAAEN,IAAI,CAACO,WAAW;UACzBC,KAAK,EAAER,IAAI,CAACS,SAAS,GAAG;YACtBC,IAAI,EAAEV,IAAI,CAACW,UAAU;YACrBC,GAAG,EAAEZ,IAAI,CAACS;UACZ,CAAC,GAAG,IAAI;UACR1E,OAAO,EAAEiE,IAAI,CAACa,gBAAgB,KAAK,CAAC;UACpCC,KAAK,EAAEd,IAAI,CAACe,WAAW;UACvBC,QAAQ,EAAE,EAAE;UAAE;UACdC,aAAa,EAAEjB,IAAI,CAACkB,cAAc;UAClCC,UAAU,EAAEnB,IAAI,CAACmB;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI3B,MAAM,EAAE;UACV;UACA4B,UAAU,CAAC,MAAM;YACfjE,QAAQ,CAACkE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGxB,QAAQ,CAAC,CAAC;YACxCtC,cAAc,CAAC,KAAK,CAAC;YACrBM,uBAAuB,CAAC,KAAK,CAAC;UAChC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLV,QAAQ,CAAC0C,QAAQ,CAAC;UAClBxC,UAAU,CAAC,KAAK,CAAC;QACnB;QAEAM,UAAU,CAAC8B,QAAQ,CAACK,IAAI,CAACwB,UAAU,CAACC,QAAQ,CAAC;QAC7C9D,cAAc,CAAC8B,IAAI,CAAC;;QAEpB;QACA,IAAIE,QAAQ,CAACK,IAAI,CAAC0B,YAAY,EAAE;UAC9B3C,cAAc,CAACY,QAAQ,CAACK,IAAI,CAAC0B,YAAY,CAAC;QAC5C;MACF,CAAC,MAAM;QACLjE,cAAc,CAAC,KAAK,CAAC;QACrBM,uBAAuB,CAAC,KAAK,CAAC;MAChC;IACF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ClE,cAAc,CAAC,KAAK,CAAC;MACrBM,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,SAAS;MACR,IAAI,CAAC2B,MAAM,EAAE;QACXnC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqE,aAAa,GAAGpH,WAAW,CAAC,MAAM;IACtC,IAAI,CAACgD,WAAW,IAAII,OAAO,EAAE;MAC3BgC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEnC,WAAW,EAAEA,WAAW,GAAG,CAAC;QAAEE;MAAQ,CAAC,CAAC;MAC/E4B,SAAS,CAAC9B,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;IAClC;EACF,CAAC,EAAE,CAAC8B,SAAS,EAAEhC,WAAW,EAAEI,OAAO,EAAEF,WAAW,CAAC,CAAC;;EAElD;EACAnD,SAAS,CAAC,MAAM;IACd,MAAMsH,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAACF,SAAS;MACpD,MAAMG,YAAY,GAAGF,QAAQ,CAACC,eAAe,CAACC,YAAY;MAC1D,MAAMC,YAAY,GAAGH,QAAQ,CAACC,eAAe,CAACE,YAAY;;MAE1D;MACA,IAAIJ,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,GAAG,EAAE;QAClDrC,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;UACjErC,WAAW;UACXI,OAAO;UACPF;QACF,CAAC,CAAC;QAEF,IAAI,CAACF,WAAW,IAAII,OAAO,EAAE;UAC3BgE,aAAa,CAAC,CAAC;QACjB;MACF;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;IAC/C,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;EACjE,CAAC,EAAE,CAACD,aAAa,EAAEpE,WAAW,EAAEI,OAAO,CAAC,CAAC;;EAEzC;EACArD,SAAS,CAAC,MAAM;IACdiF,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA;EACA,MAAMrD,WAAW,GAAG1B,OAAO,CAAC,OAAO;IACjC6H,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAMnG,iBAAiB,GAAG3B,OAAO,CAAC,OAAO;IACvC+H,IAAI,EAAE,CAAC;IACPjG,WAAW,EAAE,MAAM;IACnB,GAAGJ;EACL,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAElB;EACA,MAAMsG,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAI;MACF,MAAM/C,QAAQ,GAAG,MAAM5E,UAAU,CAAC2H,MAAM,CAAC;MACzC,IAAI/C,QAAQ,CAACG,OAAO,EAAE;QACpBzC,QAAQ,CAACD,KAAK,CAAC6C,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACX,EAAE,KAAKmD,MAAM,GACd;UACE,GAAGxC,IAAI;UACPjE,OAAO,EAAE0D,QAAQ,CAACK,IAAI,CAAC2C,QAAQ;UAC/B3B,KAAK,EAAErB,QAAQ,CAACK,IAAI,CAACiB;QACvB,CAAC,GACDf,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACLN,OAAO,CAAC+B,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMiB,gBAAgB,GAAGpI,WAAW,CAAC,MAAOkI,MAAM,IAAK;IACrD,IAAI;MACFjE,kBAAkB,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACmB,MAAM,GAAG;MAAK,CAAC,CAAC,CAAC;;MAEzD;MACA,MAAM/C,QAAQ,GAAG,MAAM1E,eAAe,CAACyH,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;MAEvD9C,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEF,QAAQ,CAAC;MAE5E,IAAIA,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAM+C,WAAW,GAAGlD,QAAQ,CAACK,IAAI,CAACkB,QAAQ,CAACjB,GAAG,CAAC6C,OAAO,KAAK;UACzDvD,EAAE,EAAEuD,OAAO,CAACvD,EAAE;UACdY,IAAI,EAAE2C,OAAO,CAACzC,SAAS;UACvBC,MAAM,EAAEwC,OAAO,CAACvC,WAAW,IAAI3F,cAAc;UAC7CmI,IAAI,EAAED,OAAO,CAACA,OAAO;UACrBE,SAAS,EAAE,IAAIC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;YACpEC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE,MAAM;YACbC,GAAG,EAAE;UACP,CAAC,CAAC;UACFpE,OAAO,EAAE4D,OAAO,CAAC5D,OAAO,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEHX,eAAe,CAACgD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAGG;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACHjD,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRlD,kBAAkB,CAAC8C,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACmB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;IAC5D;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,aAAa,GAAIb,MAAM,IAAK;IAChC,MAAMc,SAAS,GAAG,CAACtF,YAAY,CAACwE,MAAM,CAAC;IACvCvE,eAAe,CAACoD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACmB,MAAM,GAAG,CAACnB,IAAI,CAACmB,MAAM;IAAE,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIc,SAAS,IAAI,CAAClF,YAAY,CAACoE,MAAM,CAAC,EAAE;MACtCE,gBAAgB,CAACF,MAAM,CAAC;IAC1B;EACF,CAAC;EAMD,MAAMe,mBAAmB,GAAG,MAAOf,MAAM,IAAK;IAC5C,MAAMgB,WAAW,GAAG1F,UAAU,CAAC0E,MAAM,CAAC;IACtC,IAAI,CAACgB,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;MACvC/D,OAAO,CAAC+B,KAAK,CAAC,wBAAwB,CAAC;MACvC;IACF;IAEA,IAAI+B,WAAW,CAACE,MAAM,GAAG,GAAG,EAAE;MAC5BhE,OAAO,CAAC+B,KAAK,CAAC,sCAAsC,CAAC;MACrD;IACF;IAEA,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAM3E,UAAU,CAAC0H,MAAM,EAAEgB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7D,IAAIhE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACAzC,QAAQ,CAACD,KAAK,CAAC6C,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACX,EAAE,KAAKmD,MAAM,GACd;UAAE,GAAGxC,IAAI;UAAEiB,aAAa,EAAEjB,IAAI,CAACiB,aAAa,GAAG;QAAE,CAAC,GAClDjB,IACN,CAAC,CAAC;;QAEF;QACA,MAAM2D,aAAa,GAAG;UACpBtE,EAAE,EAAEI,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACvD,EAAE;UAC5BY,IAAI,EAAER,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACzC,SAAS;UACrCC,MAAM,EAAEX,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACvC,WAAW,IAAI3F,cAAc;UAC3DmI,IAAI,EAAEpD,QAAQ,CAACK,IAAI,CAAC8C,OAAO,CAACA,OAAO;UACnCE,SAAS,EAAE,UAAU;UACrB9D,OAAO,EAAEA,OAAO,CAAC;QACnB,CAAC;QAEDX,eAAe,CAACgD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAG,CAACmB,aAAa,EAAE,IAAItC,IAAI,CAACmB,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEHzE,aAAa,CAACsD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACmB,MAAM,GAAG;QAAG,CAAC,CAAC,CAAC;QAClD9C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MAC3C,CAAC,MAAM;QACLD,OAAO,CAAC+B,KAAK,CAAC,uBAAuB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMmC,iBAAiB,GAAG,MAAAA,CAAOpB,MAAM,EAAEqB,SAAS,KAAK;IACrD,MAAMC,QAAQ,GAAGpF,eAAe,CAACmF,SAAS,CAAC;IAC3C,IAAI,CAACC,QAAQ,IAAI,CAACA,QAAQ,CAACL,IAAI,CAAC,CAAC,EAAE;MACjC/D,OAAO,CAAC+B,KAAK,CAAC,wBAAwB,CAAC;MACvC;IACF;IAEA,IAAIqC,QAAQ,CAACJ,MAAM,GAAG,GAAG,EAAE;MACzBhE,OAAO,CAAC+B,KAAK,CAAC,sCAAsC,CAAC;MACrD;IACF;IAEA,IAAI;MACF,MAAMhC,QAAQ,GAAG,MAAMzE,WAAW,CAAC6I,SAAS,EAAEC,QAAQ,CAACL,IAAI,CAAC,CAAC,CAAC;MAC9D,IAAIhE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACAvB,eAAe,CAACgD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAGnB,IAAI,CAACmB,MAAM,CAAC,CAACzC,GAAG,CAAC6C,OAAO,IAChCA,OAAO,CAACvD,EAAE,KAAKwE,SAAS,GACpB;YAAE,GAAGjB,OAAO;YAAEC,IAAI,EAAEiB,QAAQ,CAACL,IAAI,CAAC;UAAE,CAAC,GACrCb,OACN;QACF,CAAC,CAAC,CAAC;QAEHnE,iBAAiB,CAAC4C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACwC,SAAS,GAAG;QAAM,CAAC,CAAC,CAAC;QAC5DlF,kBAAkB,CAAC0C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACwC,SAAS,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1DnE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC,MAAM;QACLD,OAAO,CAAC+B,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMsC,mBAAmB,GAAG,MAAAA,CAAOvB,MAAM,EAAEqB,SAAS,KAAK;IACvD,IAAI,CAAC5B,MAAM,CAAC+B,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMvE,QAAQ,GAAG,MAAMxE,aAAa,CAAC4I,SAAS,CAAC;MAC/C,IAAIpE,QAAQ,CAACG,OAAO,EAAE;QACpB;QACAzC,QAAQ,CAACD,KAAK,CAAC6C,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACX,EAAE,KAAKmD,MAAM,GACd;UAAE,GAAGxC,IAAI;UAAEiB,aAAa,EAAEjB,IAAI,CAACiB,aAAa,GAAG;QAAE,CAAC,GAClDjB,IACN,CAAC,CAAC;;QAEF;QACA3B,eAAe,CAACgD,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACmB,MAAM,GAAGnB,IAAI,CAACmB,MAAM,CAAC,CAACyB,MAAM,CAACrB,OAAO,IAAIA,OAAO,CAACvD,EAAE,KAAKwE,SAAS;QACnE,CAAC,CAAC,CAAC;QAEH1I,KAAK,CAACyE,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACLzE,KAAK,CAACsG,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CtG,KAAK,CAACsG,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAMyC,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1CzE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwE,OAAO,CAAC;;IAErD;IACAhG,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACAiD,UAAU,CAAC,YAAY;MACrB,IAAI;QACF;QACA,MAAM9B,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;QACzBnB,iBAAiB,CAAC,KAAK,CAAC;MAC1B,CAAC,CAAC,OAAOsD,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClEtD,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;EAID,MAAMiG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnH,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAMoH,WAAW,GAAG,MAAOrE,IAAI,IAAK;IAClC,IAAI;MACF;MACA,MAAMP,QAAQ,GAAG,MAAMvE,oBAAoB,CAAC8E,IAAI,CAACX,EAAE,CAAC;MAEpD,IAAII,QAAQ,CAACG,OAAO,EAAE;QACpB,MAAM0E,QAAQ,GAAG7E,QAAQ,CAACK,IAAI,CAACwE,QAAQ;;QAEvC;QACA,MAAMC,SAAS,GAAG;UAChBC,KAAK,EAAE,GAAGxE,IAAI,CAACC,IAAI,CAACC,IAAI,SAAS;UACjC2C,IAAI,EAAE7C,IAAI,CAACM,OAAO,IAAI,sBAAsB;UAC5CM,GAAG,EAAE0D;QACP,CAAC;;QAED;QACA,IAAIG,SAAS,CAACC,KAAK,EAAE;UACnB,MAAMD,SAAS,CAACC,KAAK,CAACH,SAAS,CAAC;UAChC7E,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QACpC,CAAC,MAAM;UACL;UACA;UACA,MAAM8E,SAAS,CAACE,SAAS,CAACC,SAAS,CAACN,QAAQ,CAAC;UAC7CnJ,KAAK,CAACyE,OAAO,CAAC,gCAAgC,CAAC;QACjD;MACF,CAAC,MAAM;QACLzE,KAAK,CAACsG,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAIA,KAAK,CAACvB,IAAI,KAAK,YAAY,EAAE;QAC/B/E,KAAK,CAACsG,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF;EACF,CAAC;;EAED;EACA,MAAMoD,WAAW,GAAIrE,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMsE,UAAU,GAAG;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAQ,CAAC;IAExD,IAAIxE,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MAC1B,oBAAOrF,OAAA;QAAK4J,GAAG,EAAEzE,KAAK,CAACI,GAAI;QAACtE,SAAS,EAAC,mBAAmB;QAAC4I,GAAG,EAAC,YAAY;QAAC3I,KAAK,EAAE;UAAC,GAAGuI,UAAU;UAAEK,SAAS,EAAE;QAAS;MAAE;QAAAzI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7H,CAAC,MAAM,IAAI2D,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MACjC,oBACErF,OAAA;QAAOiB,SAAS,EAAC,mBAAmB;QAAC8I,QAAQ;QAAC7I,KAAK,EAAEuI,UAAW;QAAAtI,QAAA,gBAC9DnB,OAAA;UAAQ4J,GAAG,EAAEzE,KAAK,CAACI,GAAI;UAACF,IAAI,EAAC;QAAW;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gDAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMwI,iBAAiB,GAAGA,CAAC/E,OAAO,EAAEN,IAAI,KAAK;IAC3C,IAAI,CAACM,OAAO,EAAE,OAAO,IAAI;IAEzB,MAAMgF,QAAQ,GAAGtF,IAAI,CAACQ,KAAK,KAAKR,IAAI,CAACQ,KAAK,CAACE,IAAI,KAAK,OAAO,IAAIV,IAAI,CAACQ,KAAK,CAACE,IAAI,KAAK,OAAO,CAAC;;IAE3F;IACA,IAAI,CAAC4E,QAAQ,EAAE;MACb,oBACEjK,OAAA;QAAAmB,QAAA,eACEnB,OAAA;UAAGiB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,EAAE8D;QAAO;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAEV;;IAEA;IACA,MAAM0I,cAAc,GAAGjF,OAAO,CAACoD,MAAM,GAAG,GAAG;IAC3C,MAAM8B,aAAa,GAAG1G,YAAY,CAACkB,IAAI,CAACX,EAAE,CAAC;IAC3C,MAAMoG,WAAW,GAAGD,aAAa,GAAGlF,OAAO,GAAGA,OAAO,CAACoF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,cAAc,GAAG,KAAK,GAAG,EAAE,CAAC;IAEvG,oBACElK,OAAA;MAAAmB,QAAA,eACEnB,OAAA;QAAGiB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,GAC1BiJ,WAAW,EACXF,cAAc,iBACblK,OAAA;UACEiB,SAAS,EAAC,yDAAyD;UACnER,OAAO,EAAEA,CAAA,KAAMiD,eAAe,CAACsC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACrB,IAAI,CAACX,EAAE,GAAG,CAACmG;UAAc,CAAC,CAAC,CAAE;UAAAhJ,QAAA,EAEhFgJ,aAAa,GAAG,WAAW,GAAG;QAAW;UAAA9I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8I,mBAAmB,GAAGrL,WAAW,CAAC,CAACkI,MAAM,EAAEoD,KAAK,KAAK;IACzD7H,aAAa,CAACsD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACmB,MAAM,GAAGoD;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAGvL,WAAW,CAAC,CAACwL,CAAC,EAAEtD,MAAM,KAAK;IACtD,IAAIsD,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBxC,mBAAmB,CAACf,MAAM,CAAC;IAC7B;EACF,CAAC,EAAE,CAACe,mBAAmB,CAAC,CAAC;EAEzB,MAAMyC,wBAAwB,GAAG1L,WAAW,CAAEkI,MAAM,IAAK;IACvDe,mBAAmB,CAACf,MAAM,CAAC;EAC7B,CAAC,EAAE,CAACe,mBAAmB,CAAC,CAAC;EAEzB,MAAM0C,uBAAuB,GAAG3L,WAAW,CAAC,CAACuJ,SAAS,EAAE+B,KAAK,KAAK;IAChEjH,kBAAkB,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACwC,SAAS,GAAG+B;IAAM,CAAC,CAAC,CAAC;EAC/D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,wBAAwB,GAAG5L,WAAW,CAAC,CAACwL,CAAC,EAAEtD,MAAM,EAAEqB,SAAS,KAAK;IACrE,IAAIiC,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBnC,iBAAiB,CAACpB,MAAM,EAAEqB,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAMuC,cAAc,GAAInG,IAAI,IAAK;IAC/B,IAAI,CAAChC,YAAY,CAACgC,IAAI,CAACX,EAAE,CAAC,EAAE,OAAO,IAAI;IAEvC,MAAM2B,QAAQ,GAAG5C,YAAY,CAAC4B,IAAI,CAACX,EAAE,CAAC,IAAI,EAAE;IAC5C,MAAM+G,SAAS,GAAG9H,eAAe,CAAC0B,IAAI,CAACX,EAAE,CAAC;IAE1C,oBACEhE,OAAA;MAAKiB,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCnB,OAAA;QAAIiB,SAAS,EAAC,MAAM;QAAAE,QAAA,GAAC,YAAU,EAACwD,IAAI,CAACiB,aAAa,IAAI,CAAC,EAAC,GAAC;MAAA;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG9DxB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BnB,OAAA;UAAK4J,GAAG,EAAEvK,cAAe;UAAC4B,SAAS,EAAC,qBAAqB;UAAC4I,GAAG,EAAC,SAAS;UAAC3I,KAAK,EAAE;YAACwI,KAAK,EAAE,MAAM;YAAEsB,MAAM,EAAE;UAAM;QAAE;UAAA3J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHxB,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BnB,OAAA;YACEqF,IAAI,EAAC,MAAM;YACXpE,SAAS,EAAC,cAAc;YACxBgK,WAAW,EAAC,oBAAoB;YAChCV,KAAK,EAAE9H,UAAU,CAACkC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAG;YACjCkH,QAAQ,EAAGT,CAAC,IAAKH,mBAAmB,CAAC3F,IAAI,CAACX,EAAE,EAAEyG,CAAC,CAACU,MAAM,CAACZ,KAAK,CAAE;YAC9Da,SAAS,EAAGX,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE9F,IAAI,CAACX,EAAE,CAAE;YACnDqH,SAAS,EAAE;UAAI;YAAAhK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFxB,OAAA;YAAKiB,SAAS,EAAC,iCAAiC;YAAAE,QAAA,eAC9CnB,OAAA;cAAOiB,SAAS,EAAE,CAACwB,UAAU,CAACkC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAE,EAAEqE,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;cAAAlH,QAAA,GACxF,CAACsB,UAAU,CAACkC,IAAI,CAACX,EAAE,CAAC,IAAI,EAAE,EAAEqE,MAAM,EAAC,iBACtC;YAAA;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxB,OAAA;UACEiB,SAAS,EAAC,oCAAoC;UAC9CR,OAAO,EAAEA,CAAA,KAAMkK,wBAAwB,CAAChG,IAAI,CAACX,EAAE,CAAE;UACjDsH,QAAQ,EAAE,CAAC7I,UAAU,CAACkC,IAAI,CAACX,EAAE,CAAC,IAAI,CAACvB,UAAU,CAACkC,IAAI,CAACX,EAAE,CAAC,CAACoE,IAAI,CAAC,CAAE;UAAAjH,QAAA,eAE9DnB,OAAA,CAACb,IAAI;YAACoB,IAAI,EAAC;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLuJ,SAAS,gBACR/K,OAAA;QAAKiB,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BnB,OAAA;UAAKiB,SAAS,EAAC,kCAAkC;UAACsK,IAAI,EAAC,QAAQ;UAAApK,QAAA,eAC7DnB,OAAA;YAAMiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNxB,OAAA;UAAGiB,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,gBAENxB,OAAA,CAAAE,SAAA;QAAAiB,QAAA,eAEEnB,OAAA;UACEgE,EAAE,EAAE,sBAAsBW,IAAI,CAACX,EAAE,EAAG;UAAA7C,QAAA,EAGnCwE,QAAQ,CAACjB,GAAG,CAAC6C,OAAO,iBACnBvH,OAAA;YAAsBiB,SAAS,EAAC,aAAa;YAAAE,QAAA,gBAC3CnB,OAAA;cAAK4J,GAAG,EAAErC,OAAO,CAACxC,MAAO;cAAC9D,SAAS,EAAC,qBAAqB;cAAC4I,GAAG,EAAEtC,OAAO,CAAC3C,IAAK;cAAC1D,KAAK,EAAE;gBAACwI,KAAK,EAAE,MAAM;gBAAEsB,MAAM,EAAE;cAAM;YAAE;cAAA3J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvHxB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAAAE,QAAA,gBAC/CnB,OAAA;gBAAKiB,SAAS,EAAC,kDAAkD;gBAAAE,QAAA,gBAC/DnB,OAAA;kBAAKiB,SAAS,EAAC,SAAS;kBAAAE,QAAA,EAAEoG,OAAO,CAAC3C;gBAAI;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAE5C+F,OAAO,CAAC5D,OAAO,KAAKA,OAAO,iBAC1B3D,OAAA;kBAAKiB,SAAS,EAAC,cAAc;kBAAAE,QAAA,EAC1BgC,cAAc,CAACoE,OAAO,CAACvD,EAAE,CAAC,gBACzBhE,OAAA,CAAAE,SAAA;oBAAAiB,QAAA,gBACEnB,OAAA;sBACEiB,SAAS,EAAC,wBAAwB;sBAClCR,OAAO,EAAEA,CAAA,KAAM8H,iBAAiB,CAAC5D,IAAI,CAACX,EAAE,EAAEuD,OAAO,CAACvD,EAAE,CAAE;sBAAA7C,QAAA,eAEtDnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,WAAW;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACTxB,OAAA;sBACEiB,SAAS,EAAC,0BAA0B;sBACpCR,OAAO,EAAEA,CAAA,KAAM;wBACb2C,iBAAiB,CAAC4C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAG;wBAAM,CAAC,CAAC,CAAC;wBAC7DV,kBAAkB,CAAC0C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAG;wBAAG,CAAC,CAAC,CAAC;sBAC7D,CAAE;sBAAA7C,QAAA,eAEFnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,WAAW;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA,eACT,CAAC,gBAEHxB,OAAA,CAAAE,SAAA;oBAAAiB,QAAA,gBACEnB,OAAA;sBACEiB,SAAS,EAAC,gCAAgC;sBAC1CR,OAAO,EAAEA,CAAA,KAAM;wBACb2C,iBAAiB,CAAC4C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAG;wBAAK,CAAC,CAAC,CAAC;wBAC5DV,kBAAkB,CAAC0C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACuB,OAAO,CAACvD,EAAE,GAAGuD,OAAO,CAACC;wBAAK,CAAC,CAAC,CAAC;sBACvE,CAAE;sBACF2B,KAAK,EAAC,cAAc;sBAAAhI,QAAA,eAEpBnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,YAAY;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACTxB,OAAA;sBACEiB,SAAS,EAAC,+BAA+B;sBACzCR,OAAO,EAAEA,CAAA,KAAMiI,mBAAmB,CAAC/D,IAAI,CAACX,EAAE,EAAEuD,OAAO,CAACvD,EAAE,CAAE;sBACxDmF,KAAK,EAAC,gBAAgB;sBAAAhI,QAAA,eAEtBnB,OAAA,CAACb,IAAI;wBAACoB,IAAI,EAAC,YAAY;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAEL2B,cAAc,CAACoE,OAAO,CAACvD,EAAE,CAAC,gBACzBhE,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,gBACnBnB,OAAA;kBACEqF,IAAI,EAAC,MAAM;kBACXpE,SAAS,EAAC,8BAA8B;kBACxCsJ,KAAK,EAAElH,eAAe,CAACkE,OAAO,CAACvD,EAAE,CAAC,IAAI,EAAG;kBACzCkH,QAAQ,EAAGT,CAAC,IAAKG,uBAAuB,CAACrD,OAAO,CAACvD,EAAE,EAAEyG,CAAC,CAACU,MAAM,CAACZ,KAAK,CAAE;kBACrEa,SAAS,EAAGX,CAAC,IAAKI,wBAAwB,CAACJ,CAAC,EAAE9F,IAAI,CAACX,EAAE,EAAEuD,OAAO,CAACvD,EAAE,CAAE;kBACnEqH,SAAS,EAAE,GAAI;kBACfG,SAAS;gBAAA;kBAAAnK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFxB,OAAA;kBAAKiB,SAAS,EAAC,iCAAiC;kBAAAE,QAAA,eAC9CnB,OAAA;oBAAOiB,SAAS,EAAE,CAACoC,eAAe,CAACkE,OAAO,CAACvD,EAAE,CAAC,IAAI,EAAE,EAAEqE,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;oBAAAlH,QAAA,GAChG,CAACkC,eAAe,CAACkE,OAAO,CAACvD,EAAE,CAAC,IAAI,EAAE,EAAEqE,MAAM,EAAC,iBAC9C;kBAAA;oBAAAhH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENxB,OAAA;gBAAKkB,KAAK,EAAE;kBACVuK,QAAQ,EAAE,YAAY;kBACtBC,SAAS,EAAE,YAAY;kBACvBC,YAAY,EAAE,YAAY;kBAC1BC,UAAU,EAAE,UAAU;kBACtBC,QAAQ,EAAE;gBACZ,CAAE;gBAAA1K,QAAA,EACCoG,OAAO,CAACC;cAAI;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,eAEDxB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAEoG,OAAO,CAACE;cAAS;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA,GAjFE+F,OAAO,CAACvD,EAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkFf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGC;MAAC,gBACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAID,oBACExB,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAE,QAAA,gBAC7BnB,OAAA;MAAAmB,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACRxB,OAAA;MAAKiB,SAAS,EAAC,4BAA4B;MAAAE,QAAA,eACzCnB,OAAA;QAAKiB,SAAS,EAAC,UAAU;QAAAE,QAAA,gBAEvBnB,OAAA;UAAKiB,SAAS,EAAC,wDAAwD;UAAAE,QAAA,gBACrEnB,OAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXxB,OAAA;YACEiB,SAAS,EAAC,2BAA2B;YACrCR,OAAO,EAAEsI,iBAAkB;YAC3B7H,KAAK,EAAE;cACL4K,MAAM,EAAE,SAAS;cACjBC,OAAO,EAAE,KAAK;cACdC,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE;YACd,CAAE;YACFC,YAAY,EAAGzB,CAAC,IAAKA,CAAC,CAAC0B,aAAa,CAACjL,KAAK,CAAC6F,eAAe,GAAG,SAAU;YACvEqF,YAAY,EAAG3B,CAAC,IAAKA,CAAC,CAAC0B,aAAa,CAACjL,KAAK,CAAC6F,eAAe,GAAG,aAAc;YAAA5F,QAAA,gBAE3EnB,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5BnB,OAAA;gBAAIiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,EAAE,CAAAoC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,IAAI,KAAI;cAAM;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDxB,OAAA;gBAAOiB,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNxB,OAAA;cACE4J,GAAG,EAAE,CAAArG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8I,eAAe,KAAIhN,cAAe;cACpD4B,SAAS,EAAC,gBAAgB;cAC1B4I,GAAG,EAAE,CAAAtG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsB,IAAI,KAAI,cAAe;cACzC3D,KAAK,EAAE;gBAACwI,KAAK,EAAE,MAAM;gBAAEsB,MAAM,EAAE;cAAM;YAAE;cAAA3J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA,CAACV,QAAQ;UAACgN,YAAY,EAAEzD,gBAAiB;UAACtF,WAAW,EAAEA;QAAY;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGrEqB,cAAc,iBACb7C,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAACC,KAAK,EAAE;YAChCqL,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE,oBAAoB;YAC5BzF,eAAe,EAAE;UACnB,CAAE;UAAA5F,QAAA,eACAnB,OAAA;YAAKiB,SAAS,EAAC,4BAA4B;YAAAE,QAAA,gBACzCnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAACsK,IAAI,EAAC,QAAQ;cAAApK,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNxB,OAAA;cAAIiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DxB,OAAA;cAAGiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAO,OAAO,gBACN/B,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA;YAAKiB,SAAS,EAAC,gBAAgB;YAACsK,IAAI,EAAC,QAAQ;YAAApK,QAAA,eAC3CnB,OAAA;cAAMiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNxB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,GACJK,KAAK,CAACwG,MAAM,KAAK,CAAC,gBACpBrI,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA,CAACb,IAAI;YAACoB,IAAI,EAAC,kBAAkB;YAACW,KAAK,EAAE;cAAEE,QAAQ,EAAE,MAAM;cAAEqL,KAAK,EAAE;YAAU;UAAE;YAAApL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ExB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,gBAENxB,OAAA,CAAAE,SAAA;UAAAiB,QAAA,GAEGU,KAAK,CAAC6C,GAAG,CAAC,CAACC,IAAI,EAAE+H,KAAK,kBACrB1M,OAAA;YAEEiB,SAAS,EAAC,WAAW;YACrBC,KAAK,EAAE;cACLqL,SAAS,EAAEG,KAAK,KAAK,CAAC,IAAI,CAAC7J,cAAc,GAAG,2BAA2B,GAAG,MAAM;cAChF8J,SAAS,EAAED,KAAK,KAAK,CAAC,IAAI,CAAC7J,cAAc,GAAG,eAAe,GAAG;YAChE,CAAE;YAAA1B,QAAA,eAEFnB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAE,QAAA,gBAExBnB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA;kBAAK4J,GAAG,EAAEjF,IAAI,CAACC,IAAI,CAACG,MAAO;kBAAC9D,SAAS,EAAC,qBAAqB;kBAAC4I,GAAG,EAAElF,IAAI,CAACC,IAAI,CAACC,IAAK;kBAAC3D,KAAK,EAAE;oBAACwI,KAAK,EAAE,MAAM;oBAAEsB,MAAM,EAAE,MAAM;oBAAClB,SAAS,EAAC;kBAAS;gBAAE;kBAAAzI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/IxB,OAAA;kBAAKiB,SAAS,EAAC,aAAa;kBAAAE,QAAA,gBAC1BnB,OAAA;oBAAIiB,SAAS,EAAC,MAAM;oBAAAE,QAAA,EAAEwD,IAAI,CAACC,IAAI,CAACC;kBAAI;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CxB,OAAA;oBAAOiB,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAE,IAAIuG,IAAI,CAAC/C,IAAI,CAACmB,UAAU,CAAC,CAAC8B,kBAAkB,CAAC,OAAO,EAAE;sBACrFC,IAAI,EAAE,SAAS;sBACfC,KAAK,EAAE,MAAM;sBACbC,GAAG,EAAE;oBACP,CAAC;kBAAC;oBAAA1G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,GAClB6I,iBAAiB,CAACrF,IAAI,CAACM,OAAO,EAAEN,IAAI,CAAC,EACrC6E,WAAW,CAAC7E,IAAI,CAACQ,KAAK,CAAC;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAEoE,IAAI,CAACjE,OAAO,GAAG,WAAW,GAAG,mBAAoB;kBACvDF,KAAK,EAAEmE,IAAI,CAACc,KAAM;kBAClBhF,OAAO,EAAEA,CAAA,KAAMyG,UAAU,CAACvC,IAAI,CAACX,EAAE,CAAE;kBACnCtD,OAAO,EAAEiE,IAAI,CAACjE,OAAQ;kBACtBE,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAEmE,IAAI,CAACiB,aAAa,IAAI,CAAE;kBAC/BnF,OAAO,EAAEA,CAAA,KAAMuH,aAAa,CAACrD,IAAI,CAACX,EAAE,CAAE;kBACtCpD,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,2BAA2B;kBAChCE,OAAO,EAAEA,CAAA,KAAMuI,WAAW,CAACrE,IAAI,CAAE;kBACjChE,MAAM,EAAE,IAAK;kBACbC,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLsJ,cAAc,CAACnG,IAAI,CAAC;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC,GAvDDmD,IAAI,CAACX,EAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwDT,CACN,CAAC,EAGDe,oBAAoB,iBACnBvC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAACC,KAAK,EAAE;cACvCqL,SAAS,EAAE;YACb,CAAE;YAAApL,QAAA,gBACAnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAACsK,IAAI,EAAC,QAAQ;cAAApK,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNxB,OAAA;cAAGiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN,EAEA,CAACe,oBAAoB,IAAI,CAACN,WAAW,IAAII,OAAO,iBAC/CrC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cACEiB,SAAS,EAAC,yBAAyB;cACnCR,OAAO,EAAE4F,aAAc;cAAAlF,QAAA,gBAEvBnB,OAAA,CAACb,IAAI;gBAACoB,IAAI,EAAC,kBAAkB;gBAACU,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEA,CAACa,OAAO,IAAIR,KAAK,CAACwG,MAAM,GAAG,CAAC,iBAC3BrI,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cAAGiB,SAAS,EAAC,YAAY;cAAAE,QAAA,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACN;QAAA,eAED,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,GAAA,CA3yBID,IAAI;EAAA,QACStC,WAAW;AAAA;AAAAwN,GAAA,GADxBlL,IAAI;AA6yBV,eAAeA,IAAI;AAAC,IAAApB,EAAA,EAAAmB,GAAA,EAAAmL,GAAA;AAAAC,YAAA,CAAAvM,EAAA;AAAAuM,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}