{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\public\\\\PublicCourseDetails.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FaVideo, FaDownload, FaInfinity, FaCertificate, FaShareAlt, FaTwitter, FaLinkedin, FaChevronDown, FaChevronUp, FaLock } from 'react-icons/fa';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { Icon } from '@iconify/react';\nimport courseImg from '../../assets/images/course/course1.png';\nimport '../user/course/CourseDetails.css';\nimport DefaultProfile from '../../assets/images/profile/default-profile.png';\nimport { decodeData, encodeData } from '../../utils/encodeAndEncode';\nimport { getPublicCourseDetails, AddMyCourses } from '../../services/userService';\nimport NoData from '../../components/common/NoData';\n\n// Helper function to get currency symbol\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getCurrencySymbol = currency => {\n  switch (currency === null || currency === void 0 ? void 0 : currency.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$';\n    // Default to USD symbol\n  }\n};\nconst domain = process.env.REACT_APP_DOMAIN_URL;\nfunction CourseDetails() {\n  _s();\n  var _courseData$courseMet, _courseData$courseMet2, _courseData$courseMet3, _courseData$trainer, _courseData$trainer2, _courseData$trainer3, _courseData$trainer4, _courseData$trainer5, _courseData$trainer6, _courseData$trainer7, _courseData$trainer8, _courseData$trainer9, _courseData$course_ty, _courseData$course_ty2, _courseData$course_ty3, _courseData$courseMet4, _courseData$courseMet5;\n  const navigate = useNavigate();\n  const [expandedModules, setExpandedModules] = useState(['module1']);\n  const [isEnrolling, setIsEnrolling] = useState(false);\n  const [isPaidEnrolling, setIsPaidEnrolling] = useState(false);\n  const [courseData, setCourseData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const {\n    encodedId\n  } = useParams();\n  const decoded = decodeData(encodedId);\n  const courseId = decoded === null || decoded === void 0 ? void 0 : decoded.id;\n  useEffect(() => {\n    console.log('Decoded Course ID:', courseId);\n  }, [courseId]);\n  useEffect(() => {\n    if (courseId) {\n      getCourseDetails();\n    } else {\n      setError('Invalid course URL');\n      setLoading(false);\n    }\n  }, [courseId]);\n\n  // Function to process course data and parse JSON fields\n  const processCourseData = data => {\n    if (!data) return data;\n    const processed = {\n      ...data\n    };\n\n    // Parse tags if it's a string\n    if (typeof processed.tags === 'string') {\n      try {\n        processed.tags = JSON.parse(processed.tags);\n        console.log('Parsed tags:', processed.tags);\n      } catch (error) {\n        console.error('Error parsing tags:', error);\n        processed.tags = [];\n      }\n    }\n\n    // Parse course_info if it's a string\n    if (typeof processed.course_info === 'string') {\n      try {\n        processed.course_info = JSON.parse(processed.course_info);\n        console.log('Parsed course_info:', processed.course_info);\n      } catch (error) {\n        console.error('Error parsing course_info:', error);\n        processed.course_info = [];\n      }\n    }\n    return processed;\n  };\n  async function getCourseDetails() {\n    try {\n      setLoading(true);\n      console.log('Fetching course detail......');\n      const response = await getPublicCourseDetails({\n        course_id: courseId,\n        domain: window.location.origin\n      });\n      console.log('Course detail fetched successfully-------------:', response.data);\n      if (response.success) {\n        // Process the course data to parse JSON fields\n        const processedData = processCourseData(response.data);\n        console.log('Processed course data:', processedData);\n        setCourseData(processedData);\n      } else {\n        setError('Course not available');\n      }\n    } catch (error) {\n      // Check if it's a course not found error\n      if (error.response && error.response.status === 404) {\n        setError('Course not available');\n      } else if (error.response && error.response.data && error.response.data.message === 'Course not found or inactive') {\n        setError('Course not available');\n      } else {\n        console.error('Error fetching course details:', error);\n        setError('Failed to load course');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }\n  async function EnrollFreeCourse() {\n    if (domain === 'lms.tpi.sg') {\n      window.location.href = 'https://lms.tpi.sg/';\n    } else if (domain === 'lms.nxgenvarsity.com') {\n      window.location.href = 'https://lms.nxgenvarsity.com/';\n    } else if (domain === 'lms.creatorfoundation.in') {\n      window.location.href = 'https://lms.creatorfoundation.in/';\n    } else {\n      // Default fallback (optional)\n      console.warn('Unknown domain. Redirecting to default site.');\n      window.location.href = 'https://lms.tpi.sg/';\n    }\n  }\n  const handlePaidEnrollment = async () => {\n    if (domain === 'lms.tpi.sg') {\n      window.location.href = 'https://lms.tpi.sg/';\n    } else if (domain === 'lms.nxgenvarsity.com') {\n      window.location.href = 'https://lms.nxgenvarsity.com/';\n    } else if (domain === 'lms.creatorfoundation.in') {\n      window.location.href = 'https://lms.creatorfoundation.in/';\n    } else {\n      // Default fallback\n      window.location.href = 'https://lms.tpi.sg/';\n    }\n  };\n  const toggleModule = moduleId => {\n    setExpandedModules(prev => prev.includes(moduleId) ? prev.filter(id => id !== moduleId) : [...prev, moduleId]);\n  };\n\n  // Course Data \n  const courseModules = [{\n    id: 'module1',\n    title: 'Program Information 2023/2024 Edition',\n    lectures: [{\n      title: 'About The Course',\n      duration: '01:20',\n      preview: true\n    }, {\n      title: 'Tools Introduction',\n      duration: '07:50',\n      preview: true\n    }, {\n      title: 'Basic Document Structure',\n      duration: '06:30',\n      preview: true,\n      locked: true\n    }, {\n      title: 'HTML5 Foundations Certification Final Project',\n      duration: '02:40',\n      locked: true\n    }],\n    lectureCount: '3 lectures',\n    duration: '9 min'\n  }, {\n    id: 'module2',\n    title: 'Certified HTML5 Foundations 2023/2024',\n    lectureCount: '3 lectures',\n    duration: '9 min'\n  }, {\n    id: 'module3',\n    title: 'Your Development Toolbox',\n    lectureCount: '3 lectures',\n    duration: '9 min'\n  }, {\n    id: 'module4',\n    title: 'JavaScript Specialist',\n    lectureCount: '3 lectures',\n    duration: '9 min'\n  }];\n  const handleWatchCourse = () => {\n    navigate('/user/courses/courseDetails/WatchCourse');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-3 text-muted\",\n              children: \"Loading course...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-5\",\n            children: error === 'Course not available' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"mdi:book-outline\",\n                style: {\n                  fontSize: '4rem',\n                  color: '#6c757d'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-3 text-muted\",\n                children: \"Course Not Available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"This course may have been deleted or is no longer accessible.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary mt-3\",\n                onClick: () => navigate('/'),\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"mdi:home\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), \"Go to Home\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"mdi:alert-circle-outline\",\n                style: {\n                  fontSize: '4rem',\n                  color: '#dc3545'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-3 text-danger\",\n                children: \"Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this);\n  }\n  if (!courseData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-5\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:book-outline\",\n              style: {\n                fontSize: '4rem',\n                color: '#6c757d'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-3 text-muted\",\n              children: \"Course Not Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"The course you're looking for doesn't exist or has been removed.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this);\n  }\n  const hasSubCategories = courseData.course_subcategory && (() => {\n    try {\n      const parsed = JSON.parse(courseData.course_subcategory);\n      return Array.isArray(parsed) && parsed.length > 0;\n    } catch (e) {\n      console.error('Error parsing subcategories:', e);\n      return false;\n    }\n  })();\n  const hasModules = courseData.modules && Object.keys(courseData.modules).length > 0;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-details-header px-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"fw-bold mb-3\",\n              style: {\n                wordWrap: \"break-word\",\n                overflowWrap: \"break-word\",\n                whiteSpace: \"normal\"\n              },\n              children: courseData.course_name || \"Untitled Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-size-8\",\n              children: courseData.tags && Array.isArray(courseData.tags) ? courseData.tags.join(' | ') : 'No tags available'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-wrap align-items-center gap-4 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:star\",\n                  className: \"text-warning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ms-2\",\n                  children: [(courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet = courseData.courseMeta) === null || _courseData$courseMet === void 0 ? void 0 : _courseData$courseMet.averageRating) || '0.0', \" rating\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:menu-book\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [(courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet2 = courseData.courseMeta) === null || _courseData$courseMet2 === void 0 ? void 0 : _courseData$courseMet2.modulesCount) || 0, \" Modules\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:update\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Duration: \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet3 = courseData.courseMeta) === null || _courseData$courseMet3 === void 0 ? void 0 : _courseData$courseMet3.totalVideoDuration) || '00:00:00']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:school\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Level: \", (courseData === null || courseData === void 0 ? void 0 : courseData.levels) || 'Not specified']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:language\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Language: \", (courseData === null || courseData === void 0 ? void 0 : courseData.course_language) || 'Not specified']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instructor d-flex align-items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instructor-image\",\n                style: {\n                  width: '40px',\n                  height: '40px',\n                  minWidth: '40px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer = courseData.trainer) === null || _courseData$trainer === void 0 ? void 0 : _courseData$trainer.profile) || DefaultProfile,\n                  alt: courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer2 = courseData.trainer) === null || _courseData$trainer2 === void 0 ? void 0 : _courseData$trainer2.name,\n                  className: \"rounded-circle\",\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-column\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-size-1\",\n                  children: [\"By \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer3 = courseData.trainer) === null || _courseData$trainer3 === void 0 ? void 0 : _courseData$trainer3.name) || 'Loading...']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-size-1\",\n                  children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer4 = courseData.trainer) === null || _courseData$trainer4 === void 0 ? void 0 : _courseData$trainer4.role) || 'Instructor'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row justify-content-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-11\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-details-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-lg-8 px-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"what-youll-learn mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-3\",\n                      children: \"Course Info\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"learning-points\",\n                      children: courseData.course_info && Array.isArray(courseData.course_info) ? courseData.course_info.map((point, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"learning-point mb-3 d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Icon, {\n                          icon: \"material-symbols:check-circle\",\n                          className: \"text-success me-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 362,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: point\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 29\n                      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"learning-point mb-3 d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Icon, {\n                          icon: \"material-symbols:check-circle\",\n                          className: \"text-success me-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 368,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Course information not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 369,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 21\n                  }, this), courseData.course_desc ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"about-course mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-3\",\n                      children: \"About This Course\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"about-content\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-muted mb-3\",\n                        style: {\n                          wordBreak: 'break-word',\n                          whiteSpace: 'normal'\n                        },\n                        children: courseData.course_desc\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-content mb-4 shadow-none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-3\",\n                      children: \"Course Content\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 23\n                    }, this), hasModules ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"modules-list\",\n                      children: Object.entries(courseData.modules).map(([moduleName, contents]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"module-item\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"module-header d-flex align-items-center justify-content-between p-3\",\n                          onClick: () => toggleModule(moduleName),\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center\",\n                            children: [expandedModules.includes(moduleName) ? /*#__PURE__*/_jsxDEV(FaChevronDown, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 405,\n                              columnNumber: 75\n                            }, this) : /*#__PURE__*/_jsxDEV(FaChevronUp, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 405,\n                              columnNumber: 95\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"ms-2\",\n                              children: moduleName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 406,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 404,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"module-meta text-muted\",\n                            children: /*#__PURE__*/_jsxDEV(\"small\", {\n                              children: [contents.length, \" items\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 409,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 408,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 399,\n                          columnNumber: 31\n                        }, this), expandedModules.includes(moduleName) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"module-content\",\n                          children: contents.map((content, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"lecture-item d-flex align-items-center justify-content-between p-3\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex align-items-center\",\n                              children: [content.type === 'Video' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:play-circle-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 418,\n                                columnNumber: 43\n                              }, this), content.type === 'Document' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:description-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 421,\n                                columnNumber: 43\n                              }, this), content.type === 'Assessment' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:quiz-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 424,\n                                columnNumber: 43\n                              }, this), content.type === 'Survey' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:analytics-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 427,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: content.title\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 429,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 416,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex align-items-center\",\n                              children: [content.type === 'Video' && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-3 text-muted\",\n                                children: content.duration\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 433,\n                                columnNumber: 43\n                              }, this), content.type === 'Document' && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-3 text-muted\",\n                                children: content.fileType\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 436,\n                                columnNumber: 43\n                              }, this), content.type === 'Assessment' && content.questions && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-3 text-muted\",\n                                children: content.questions\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 439,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 431,\n                              columnNumber: 39\n                            }, this)]\n                          }, idx, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 415,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 413,\n                          columnNumber: 33\n                        }, this)]\n                      }, moduleName, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(NoData, {\n                      caption: \"No course content available yet\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this), courseData.trainer ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"instructor-profile mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-4\",\n                      children: \"Instructor\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex flex-column flex-md-row gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"instructor-image-lg d-none d-md-block\",\n                        style: {\n                          width: '150px',\n                          height: '150px',\n                          minWidth: '150px',\n                          overflow: 'hidden',\n                          borderRadius: '12px',\n                          position: 'relative',\n                          backgroundColor: '#f8f9fa'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer5 = courseData.trainer) === null || _courseData$trainer5 === void 0 ? void 0 : _courseData$trainer5.profile) || DefaultProfile,\n                          alt: courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer6 = courseData.trainer) === null || _courseData$trainer6 === void 0 ? void 0 : _courseData$trainer6.name,\n                          style: {\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 473,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 461,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"instructor-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"h5 mb-1\",\n                          style: {\n                            textAlign: 'left'\n                          },\n                          children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer7 = courseData.trainer) === null || _courseData$trainer7 === void 0 ? void 0 : _courseData$trainer7.name) || 'Loading...'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 486,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted mb-3\",\n                          style: {\n                            textAlign: 'left'\n                          },\n                          children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer8 = courseData.trainer) === null || _courseData$trainer8 === void 0 ? void 0 : _courseData$trainer8.role) || 'Fire Management Specialist & Environmental Trainer'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 487,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted mb-4\",\n                          style: {\n                            textAlign: 'left'\n                          },\n                          children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer9 = courseData.trainer) === null || _courseData$trainer9 === void 0 ? void 0 : _courseData$trainer9.bio) || 'No bio available'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 516,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(NoData, {\n                    caption: \"Instructor information not available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-lg-4 mt-5 mt-lg-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-card card shadow-sm\",\n                    style: {\n                      position: 'relative',\n                      top: '-5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: courseData.banner_image || courseImg,\n                      className: \"img-fluid border-2 m-2\",\n                      alt: courseData.course_name || \"Course Preview\",\n                      onError: e => {\n                        e.target.src = courseImg;\n                        e.target.onerror = null;\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"price-tag\",\n                          children: [getCurrencySymbol(courseData === null || courseData === void 0 ? void 0 : courseData.currency), \" \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$course_ty = courseData.course_type) === null || _courseData$course_ty === void 0 ? void 0 : _courseData$course_ty.toLowerCase()) === 'free' ? '0' : (courseData === null || courseData === void 0 ? void 0 : courseData.course_price) || '0']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 542,\n                          columnNumber: 27\n                        }, this), (courseData === null || courseData === void 0 ? void 0 : (_courseData$course_ty2 = courseData.course_type) === null || _courseData$course_ty2 === void 0 ? void 0 : _courseData$course_ty2.toLowerCase()) === 'free' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"original-price\",\n                          children: [getCurrencySymbol(courseData === null || courseData === void 0 ? void 0 : courseData.currency), \"0\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 544,\n                          columnNumber: 29\n                        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"original-price\",\n                          children: [getCurrencySymbol(courseData === null || courseData === void 0 ? void 0 : courseData.currency), Number((courseData === null || courseData === void 0 ? void 0 : courseData.course_price) || 0) + 5]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 546,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 541,\n                        columnNumber: 25\n                      }, this), (courseData === null || courseData === void 0 ? void 0 : (_courseData$course_ty3 = courseData.course_type) === null || _courseData$course_ty3 === void 0 ? void 0 : _courseData$course_ty3.toLowerCase()) === 'paid' ? /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handlePaidEnrollment,\n                        className: \"paid-btn\",\n                        disabled: isPaidEnrolling,\n                        children: isPaidEnrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"spinner-border spinner-border-sm me-2\",\n                            role: \"status\",\n                            \"aria-hidden\": \"true\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 558,\n                            columnNumber: 33\n                          }, this), \"Processing...\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Icon, {\n                            icon: \"mdi:lock\",\n                            className: \"btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 563,\n                            columnNumber: 33\n                          }, this), \"Enroll Now\"]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 551,\n                        columnNumber: 27\n                      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"watch-now-btn free-btn\",\n                        onClick: EnrollFreeCourse,\n                        disabled: isEnrolling,\n                        children: isEnrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"spinner-border spinner-border-sm me-2\",\n                            role: \"status\",\n                            \"aria-hidden\": \"true\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 579,\n                            columnNumber: 33\n                          }, this), \"Enrolling...\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Icon, {\n                            icon: \"mdi:play-circle\",\n                            className: \"btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 584,\n                            columnNumber: 33\n                          }, this), courseData.is_course_purchase ? 'Continue Learning' : 'Watch Now']\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 572,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-center align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaInfinity, {\n                          className: \"text-muted me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 596,\n                          columnNumber: 27\n                        }, this), \"Full lifetime access\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"course-includes\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"h6 mb-3\",\n                          children: \"This course includes:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 27\n                        }, this), hasModules ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled\",\n                          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:menu-book\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 605,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet4 = courseData.courseMeta) === null || _courseData$courseMet4 === void 0 ? void 0 : _courseData$courseMet4.modulesCount) || 0, \" Modules\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 604,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 609,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Video').length || 0, \" Videos \\u2022 \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet5 = courseData.courseMeta) === null || _courseData$courseMet5 === void 0 ? void 0 : _courseData$courseMet5.totalVideoDuration) || '00:00:00', \" total duration\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 608,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:quiz\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 613,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Assessment').length || 0, \" Assessments\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 612,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:description-outline\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 617,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Document').length || 0, \" Documents\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 616,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:analytics-outline\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 621,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Survey').length || 0, \" Surveys\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 620,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaInfinity, {\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 629,\n                              columnNumber: 33\n                            }, this), \"Full lifetime access\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 628,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaCertificate, {\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 633,\n                              columnNumber: 33\n                            }, this), \"Certificate of completion\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 632,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 603,\n                          columnNumber: 29\n                        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted\",\n                          children: \"Course content is being prepared\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 638,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 540,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(CourseDetails, \"NxZHMTbKJ6IVeqkR9Z8FqWdz9I4=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = CourseDetails;\nexport default CourseDetails;\nvar _c;\n$RefreshReg$(_c, \"CourseDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaVideo", "FaDownload", "FaInfinity", "FaCertificate", "FaShareAlt", "FaTwitter", "FaLinkedin", "FaChevronDown", "FaChevronUp", "FaLock", "useNavigate", "useParams", "toast", "Icon", "courseImg", "DefaultProfile", "decodeData", "encodeData", "getPublicCourseDetails", "AddMyCourses", "NoData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getCurrencySymbol", "currency", "toUpperCase", "domain", "process", "env", "REACT_APP_DOMAIN_URL", "CourseDetails", "_s", "_courseData$courseMet", "_courseData$courseMet2", "_courseData$courseMet3", "_courseData$trainer", "_courseData$trainer2", "_courseData$trainer3", "_courseData$trainer4", "_courseData$trainer5", "_courseData$trainer6", "_courseData$trainer7", "_courseData$trainer8", "_courseData$trainer9", "_courseData$course_ty", "_courseData$course_ty2", "_courseData$course_ty3", "_courseData$courseMet4", "_courseData$courseMet5", "navigate", "expandedModules", "setExpandedModules", "isEnrolling", "setIsEnrolling", "isPaidEnrolling", "setIsPaidEnrolling", "courseData", "setCourseData", "loading", "setLoading", "error", "setError", "encodedId", "decoded", "courseId", "id", "console", "log", "getCourseDetails", "processCourseData", "data", "processed", "tags", "JSON", "parse", "course_info", "response", "course_id", "window", "location", "origin", "success", "processedData", "status", "message", "EnrollFreeCourse", "href", "warn", "handlePaidEnrollment", "toggleModule", "moduleId", "prev", "includes", "filter", "courseModules", "title", "lectures", "duration", "preview", "locked", "lectureCount", "handleWatchCourse", "className", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "style", "fontSize", "color", "onClick", "hasSubCategories", "course_subcategory", "parsed", "Array", "isArray", "length", "e", "hasModules", "modules", "Object", "keys", "wordWrap", "overflowWrap", "whiteSpace", "course_name", "join", "courseMeta", "averageRating", "modulesCount", "totalVideoDuration", "levels", "course_language", "width", "height", "min<PERSON><PERSON><PERSON>", "src", "trainer", "profile", "alt", "name", "objectFit", "map", "point", "index", "course_desc", "wordBreak", "entries", "moduleName", "contents", "cursor", "content", "idx", "type", "fileType", "questions", "caption", "overflow", "borderRadius", "position", "backgroundColor", "textAlign", "bio", "top", "banner_image", "onError", "target", "onerror", "course_type", "toLowerCase", "course_price", "Number", "disabled", "is_course_purchase", "values", "flat", "item", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/public/PublicCourseDetails.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  FaVideo, FaDownload, FaInfinity, FaCertificate, FaShareAlt,\n  FaTwitter, FaLinkedin, FaChevronDown, FaChevronUp, FaLock\n} from 'react-icons/fa';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { Icon } from '@iconify/react';\nimport courseImg from '../../assets/images/course/course1.png';\nimport '../user/course/CourseDetails.css';\nimport DefaultProfile from '../../assets/images/profile/default-profile.png';\nimport { decodeData, encodeData } from '../../utils/encodeAndEncode';\nimport { getPublicCourseDetails, AddMyCourses } from '../../services/userService';\nimport NoData from '../../components/common/NoData';\n\n// Helper function to get currency symbol\nconst getCurrencySymbol = (currency) => {\n  switch (currency?.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$'; // Default to USD symbol\n  }\n};\n\nconst domain = process.env.REACT_APP_DOMAIN_URL;\n\nfunction CourseDetails() {\n\n\n  const navigate = useNavigate(); \n  const [expandedModules, setExpandedModules] = useState(['module1']);\n  const [isEnrolling, setIsEnrolling] = useState(false);\n  const [isPaidEnrolling, setIsPaidEnrolling] = useState(false);\n  const [courseData, setCourseData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n\n  const { encodedId } = useParams();\n  const decoded = decodeData(encodedId);\n  const courseId = decoded?.id;\n\n  useEffect(() => {\n    console.log('Decoded Course ID:', courseId);\n  }, [courseId]);\n\n  useEffect(() => {\n    if (courseId) {\n      getCourseDetails();\n    } else {\n      setError('Invalid course URL');\n      setLoading(false);\n    }\n  }, [courseId]);\n\n\n  // Function to process course data and parse JSON fields\n  const processCourseData = (data) => {\n    if (!data) return data;\n    \n    const processed = { ...data };\n    \n    // Parse tags if it's a string\n    if (typeof processed.tags === 'string') {\n      try {\n        processed.tags = JSON.parse(processed.tags);\n        console.log('Parsed tags:', processed.tags);\n      } catch (error) {\n        console.error('Error parsing tags:', error);\n        processed.tags = [];\n      }\n    }\n    \n    // Parse course_info if it's a string\n    if (typeof processed.course_info === 'string') {\n      try {\n        processed.course_info = JSON.parse(processed.course_info);\n        console.log('Parsed course_info:', processed.course_info);\n      } catch (error) {\n        console.error('Error parsing course_info:', error);\n        processed.course_info = [];\n      }\n    }\n    \n    return processed;\n  };\n\n  async function getCourseDetails() {\n    try {\n      setLoading(true);\n      console.log('Fetching course detail......');\n      const response = await getPublicCourseDetails({\n        course_id: courseId,\n        domain : window.location.origin\n      });\n      console.log('Course detail fetched successfully-------------:', response.data);\n      \n      if (response.success) {\n        // Process the course data to parse JSON fields\n        const processedData = processCourseData(response.data);\n        console.log('Processed course data:', processedData);\n        setCourseData(processedData);\n      } else {\n        setError('Course not available');\n      }\n    } catch (error) {\n      // Check if it's a course not found error\n      if (error.response && error.response.status === 404) {\n        setError('Course not available');\n      } else if (error.response && error.response.data && error.response.data.message === 'Course not found or inactive') {\n        setError('Course not available');\n      } else {\n        console.error('Error fetching course details:', error);\n        setError('Failed to load course');\n      }\n    } finally {\n      setLoading(false);\n    }\n  }\n\n\n  async function EnrollFreeCourse() {\n    if (domain === 'lms.tpi.sg') {\n      window.location.href = 'https://lms.tpi.sg/';\n    } else if (domain === 'lms.nxgenvarsity.com') {\n      window.location.href = 'https://lms.nxgenvarsity.com/';\n    } else if (domain === 'lms.creatorfoundation.in') {\n      window.location.href = 'https://lms.creatorfoundation.in/';\n    } else {\n      // Default fallback (optional)\n      console.warn('Unknown domain. Redirecting to default site.');\n      window.location.href = 'https://lms.tpi.sg/';\n    }\n  }\n  \n  const handlePaidEnrollment = async () => {\n    if (domain === 'lms.tpi.sg') {\n      window.location.href = 'https://lms.tpi.sg/';\n    } else if (domain === 'lms.nxgenvarsity.com') {\n      window.location.href = 'https://lms.nxgenvarsity.com/';\n    } else if (domain === 'lms.creatorfoundation.in') {\n      window.location.href = 'https://lms.creatorfoundation.in/';\n    } else {\n      // Default fallback\n      window.location.href = 'https://lms.tpi.sg/';\n    }\n  };\n  \n\n  const toggleModule = (moduleId) => {\n    setExpandedModules(prev =>\n      prev.includes(moduleId)\n        ? prev.filter(id => id !== moduleId)\n        : [...prev, moduleId]\n    );\n  };\n\n  // Course Data \n  const courseModules = [\n    {\n      id: 'module1',\n      title: 'Program Information 2023/2024 Edition',\n      lectures: [\n        { title: 'About The Course', duration: '01:20', preview: true },\n        { title: 'Tools Introduction', duration: '07:50', preview: true },\n        { title: 'Basic Document Structure', duration: '06:30', preview: true, locked: true },\n        { title: 'HTML5 Foundations Certification Final Project', duration: '02:40', locked: true }\n      ],\n      lectureCount: '3 lectures',\n      duration: '9 min'\n    },\n    {\n      id: 'module2',\n      title: 'Certified HTML5 Foundations 2023/2024',\n      lectureCount: '3 lectures',\n      duration: '9 min'\n    },\n    {\n      id: 'module3',\n      title: 'Your Development Toolbox',\n      lectureCount: '3 lectures',\n      duration: '9 min'\n    },\n    {\n      id: 'module4',\n      title: 'JavaScript Specialist',\n      lectureCount: '3 lectures',\n      duration: '9 min'\n    }\n  ];\n\n  const handleWatchCourse = () => {\n    navigate('/user/courses/courseDetails/WatchCourse');\n  }\n\n  if (loading) {\n    return (\n      <div className=\"container py-5\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-md-8\">\n            <div className=\"text-center py-5\">\n              <div className=\"spinner-border text-primary\" role=\"status\">\n                <span className=\"visually-hidden\">Loading...</span>\n              </div>\n              <p className=\"mt-3 text-muted\">Loading course...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"container py-5\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-md-8\">\n            <div className=\"text-center py-5\">\n              {error === 'Course not available' ? (\n                <>\n                  <Icon icon=\"mdi:book-outline\" style={{ fontSize: '4rem', color: '#6c757d' }} />\n                  <h3 className=\"mt-3 text-muted\">Course Not Available</h3>\n                  <p className=\"text-muted\">This course may have been deleted or is no longer accessible.</p>\n                  <button \n                    className=\"btn btn-primary mt-3\"\n                    onClick={() => navigate('/')}\n                  >\n                    <Icon icon=\"mdi:home\" className=\"me-2\" />\n                    Go to Home\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Icon icon=\"mdi:alert-circle-outline\" style={{ fontSize: '4rem', color: '#dc3545' }} />\n                  <h3 className=\"mt-3 text-danger\">Error</h3>\n                  <p className=\"text-muted\">{error}</p>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!courseData) {\n    return (\n      <div className=\"container py-5\">\n        <div className=\"row justify-content-center\">\n          <div className=\"col-md-8\">\n            <div className=\"text-center py-5\">\n              <Icon icon=\"mdi:book-outline\" style={{ fontSize: '4rem', color: '#6c757d' }} />\n              <h3 className=\"mt-3 text-muted\">Course Not Found</h3>\n              <p className=\"text-muted\">The course you're looking for doesn't exist or has been removed.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const hasSubCategories = courseData.course_subcategory &&\n    (() => {\n      try {\n        const parsed = JSON.parse(courseData.course_subcategory);\n        return Array.isArray(parsed) && parsed.length > 0;\n      } catch (e) {\n        console.error('Error parsing subcategories:', e);\n        return false;\n      }\n    })();\n\n  const hasModules = courseData.modules &&\n    Object.keys(courseData.modules).length > 0;\n\n  return (\n    <>\n      <div className=\"row\">\n        <div className=\"col-12 p-0\">\n          <div className=\"course-details-header px-4\">\n            <div className=\"container\">\n            <h3\n  className=\"fw-bold mb-3\"\n  style={{\n    wordWrap: \"break-word\",\n    overflowWrap: \"break-word\",\n    whiteSpace: \"normal\",\n  }}\n>\n  {courseData.course_name || \"Untitled Course\"}\n</h3>\n              <p className=\"font-size-8\">\n                {courseData.tags && Array.isArray(courseData.tags) ? (\n                  courseData.tags.join(' | ')\n                ) : (\n                  'No tags available'\n                )}\n              </p>\n              <div className=\"d-flex flex-wrap align-items-center gap-4 mb-3\">\n                <div className=\"d-flex align-items-center\">\n                  <Icon icon=\"material-symbols:star\" className=\"text-warning\" />\n                  <span className=\"ms-2\">{courseData?.courseMeta?.averageRating || '0.0'} rating</span>\n                </div>\n                <div className=\"d-flex align-items-center\">\n                  <Icon icon=\"material-symbols:menu-book\" className=\"me-2\" />\n                  <span>{courseData?.courseMeta?.modulesCount || 0} Modules</span>\n                </div>\n                {/* <div className=\"d-flex align-items-center\">\n                  <Icon icon=\"material-symbols:group\" className=\"me-2\" />\n                  <span>{courseData?.courseMeta?.enrolledCount || 0} Students</span>\n                </div> */}\n                <div className=\"d-flex align-items-center\">\n                  <Icon icon=\"material-symbols:update\" className=\"me-2\" />\n                  <span>Duration: {courseData?.courseMeta?.totalVideoDuration || '00:00:00'}</span>\n                </div>\n                <div className=\"d-flex align-items-center\">\n                  <Icon icon=\"material-symbols:school\" className=\"me-2\" />\n                  <span>Level: {courseData?.levels || 'Not specified'}</span>\n                </div>\n                <div className=\"d-flex align-items-center\">\n                  <Icon icon=\"material-symbols:language\" className=\"me-2\" />\n                  <span>Language: {courseData?.course_language || 'Not specified'}</span>\n                </div>\n              </div>\n              <div className=\"instructor d-flex align-items-center gap-2\">\n                <div className=\"instructor-image\" style={{ width: '40px', height: '40px', minWidth: '40px' }}>\n                  <img\n                    src={courseData?.trainer?.profile || DefaultProfile}\n                    alt={courseData?.trainer?.name}\n                    className=\"rounded-circle\"\n                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}\n                  />\n                </div>\n                <div className='d-flex flex-column'>\n                  <span className='font-size-1'>By {courseData?.trainer?.name || 'Loading...'}</span>\n                  <span className='font-size-1'>{courseData?.trainer?.role || 'Instructor'}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"row justify-content-center\">\n            <div className=\"col-12 col-md-11\">\n              <div className=\"course-details-container\">\n                <div className=\"row\">\n                  <div className=\"col-lg-8 px-4\">\n                    <div className=\"what-youll-learn mb-4\">\n                      <h3 className=\"h4 mb-3\">Course Info</h3>\n                      <div className=\"learning-points\">\n                        {courseData.course_info && Array.isArray(courseData.course_info) ? (\n                          courseData.course_info.map((point, index) => (\n                            <div key={index} className=\"learning-point mb-3 d-flex align-items-center\">\n                              <Icon icon=\"material-symbols:check-circle\" className=\"text-success me-3\" />\n                              <span>{point}</span>\n                            </div>\n                          ))\n                        ) : (\n                          <div className=\"learning-point mb-3 d-flex align-items-center\">\n                            <Icon icon=\"material-symbols:check-circle\" className=\"text-success me-3\" />\n                            <span>Course information not available</span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n\n                    {courseData.course_desc ? (\n                      <div className=\"about-course mb-4\">\n                        <h3 className=\"h4 mb-3\">About This Course</h3>\n                        <div className=\"about-content\">\n                          <p\n                            className=\"text-muted mb-3\"\n                            style={{\n                              wordBreak: 'break-word',\n                              whiteSpace: 'normal',\n                            }}\n                          >\n                            {courseData.course_desc}\n                          </p>\n                        </div>\n                      </div>\n                    ) : null}\n\n\n                    <div className=\"course-content mb-4 shadow-none\">\n                      <h3 className=\"h4 mb-3\">Course Content</h3>\n                      {hasModules ? (\n                        <div className=\"modules-list\">\n                          {Object.entries(courseData.modules).map(([moduleName, contents]) => (\n                            <div key={moduleName} className=\"module-item\">\n                              <div\n                                className=\"module-header d-flex align-items-center justify-content-between p-3\"\n                                onClick={() => toggleModule(moduleName)}\n                                style={{ cursor: 'pointer' }}\n                              >\n                                <div className=\"d-flex align-items-center\">\n                                  {expandedModules.includes(moduleName) ? <FaChevronDown /> : <FaChevronUp />}\n                                  <span className=\"ms-2\">{moduleName}</span>\n                                </div>\n                                <div className=\"module-meta text-muted\">\n                                  <small>{contents.length} items</small>\n                                </div>\n                              </div>\n                              {expandedModules.includes(moduleName) && (\n                                <div className=\"module-content\">\n                                  {contents.map((content, idx) => (\n                                    <div key={idx} className=\"lecture-item d-flex align-items-center justify-content-between p-3\">\n                                      <div className=\"d-flex align-items-center\">\n                                        {content.type === 'Video' && (\n                                          <Icon icon=\"material-symbols:play-circle-outline\" className=\"me-2\" />\n                                        )}\n                                        {content.type === 'Document' && (\n                                          <Icon icon=\"material-symbols:description-outline\" className=\"me-2\" />\n                                        )}\n                                        {content.type === 'Assessment' && (\n                                          <Icon icon=\"material-symbols:quiz-outline\" className=\"me-2\" />\n                                        )}\n                                        {content.type === 'Survey' && (\n                                          <Icon icon=\"material-symbols:analytics-outline\" className=\"me-2\" />\n                                        )}\n                                        <span>{content.title}</span>\n                                      </div>\n                                      <div className=\"d-flex align-items-center\">\n                                        {content.type === 'Video' && (\n                                          <span className=\"ms-3 text-muted\">{content.duration}</span>\n                                        )}\n                                        {content.type === 'Document' && (\n                                          <span className=\"ms-3 text-muted\">{content.fileType}</span>\n                                        )}\n                                        {content.type === 'Assessment' && content.questions && (\n                                          <span className=\"ms-3 text-muted\">{content.questions}</span>\n                                        )}\n                                      </div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <NoData caption=\"No course content available yet\" />\n                      )}\n                    </div>\n\n                    {courseData.trainer ? (\n                      <div className=\"instructor-profile mb-4\">\n                        <h3 className=\"h4 mb-4\">Instructor</h3>\n                        <div className=\"d-flex flex-column flex-md-row gap-4\">\n\n                          {/* Image Wrapper */}\n                          <div\n                            className=\"instructor-image-lg d-none d-md-block\"\n                            style={{\n                              width: '150px',\n                              height: '150px',\n                              minWidth: '150px',\n                              overflow: 'hidden',\n                              borderRadius: '12px',\n                              position: 'relative',\n                              backgroundColor: '#f8f9fa'\n                            }}\n                          >\n                            <img\n                              src={courseData?.trainer?.profile || DefaultProfile}\n                              alt={courseData?.trainer?.name}\n                              style={{\n                                width: '100%',\n                                height: '100%',\n                                objectFit: 'cover'\n                              }}\n                            />\n                          </div>\n\n                          {/* Instructor Info */}\n                          <div className=\"instructor-info\">\n                            <h4 className=\"h5 mb-1\" style={{textAlign: 'left'}}>{courseData?.trainer?.name || 'Loading...'}</h4>\n                            <p className=\"text-muted mb-3\" style={{textAlign: 'left'}}>\n                              {courseData?.trainer?.role || 'Fire Management Specialist & Environmental Trainer'}\n                            </p>\n\n                            {/* <div className=\"d-flex flex-wrap align-items-center gap-4 mb-3\"> */}\n\n                            {/* <div className=\"d-flex align-items-center\">\n                                <span className=\"me-2\">4.5</span>\n                                <div className=\"rating-stars\">\n                                  <Icon icon=\"material-symbols:star\" className=\"text-warning\" />\n                                  <Icon icon=\"material-symbols:star\" className=\"text-warning\" />\n                                  <Icon icon=\"material-symbols:star\" className=\"text-warning\" />\n                                  <Icon icon=\"material-symbols:star\" className=\"text-warning\" />\n                                  <Icon icon=\"material-symbols:star-half\" className=\"text-warning\" />\n                                </div>\n                                <span className=\"ms-2\">6 Reviews</span>\n                              </div> */}\n                            {/* \n                              <div className=\"d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:group\" className=\"me-2\" />\n                                <span>0 Students</span>\n                              </div>\n\n                              <div className=\"d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:play-circle\" className=\"me-2\" />\n                                <span>0 Courses</span>\n                              </div> */}\n                            {/* </div> */}\n\n                            <p className=\"text-muted mb-4\" style={{textAlign:'left'}}>  \n                              {courseData?.trainer?.bio || 'No bio available'}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n\n\n                    ) : (\n                      <NoData caption=\"Instructor information not available\" />\n                    )}\n                  </div>\n\n                  <div className=\"col-lg-4 mt-5 mt-lg-0\">\n                    <div className=\"course-card card shadow-sm\" style={{ position: 'relative', top: '-5rem' }}>\n                      <img\n                        src={courseData.banner_image || courseImg}\n                        className=\"img-fluid border-2 m-2\"\n                        alt={courseData.course_name || \"Course Preview\"}\n                        onError={(e) => {\n                          e.target.src = courseImg;\n                          e.target.onerror = null;\n                        }}\n                      />\n                      <div className=\"card-body\">\n                        <div className=\"d-flex justify-content-between align-items-center mb-3\">\n                          <span className=\"price-tag\">{getCurrencySymbol(courseData?.currency)} {courseData?.course_type?.toLowerCase() === 'free' ? '0' : courseData?.course_price || '0'}</span>\n                          {courseData?.course_type?.toLowerCase() === 'free' ? (\n                            <span className=\"original-price\">{getCurrencySymbol(courseData?.currency)}0</span>\n                          ) : (\n                            <span className=\"original-price\">{getCurrencySymbol(courseData?.currency)}{Number(courseData?.course_price || 0) + 5}</span>\n                          )}\n                        </div>\n\n                        {courseData?.course_type?.toLowerCase() === 'paid' ? (\n                          <button\n                            onClick={handlePaidEnrollment}\n                            className=\"paid-btn\"\n                            disabled={isPaidEnrolling}\n                          >\n                            {isPaidEnrolling ? (\n                              <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                Processing...\n                              </>\n                            ) : (\n                              <>\n                                <Icon\n                                  icon=\"mdi:lock\"\n                                  className=\"btn-icon\"\n                                />\n                                Enroll Now\n                              </>\n                            )}\n                          </button>\n                        ) : (\n                          <button\n                            className=\"watch-now-btn free-btn\"\n                            onClick={EnrollFreeCourse}\n                            disabled={isEnrolling}\n                          >\n                            {isEnrolling ? (\n                              <>\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                                Enrolling...\n                              </>\n                            ) : (\n                              <>\n                                <Icon\n                                  icon=\"mdi:play-circle\"\n                                  className=\"btn-icon\"\n                                />\n                                {courseData.is_course_purchase ? 'Continue Learning' : 'Watch Now'}\n                              </>\n                            )}\n                          </button>\n                        )}\n\n\n                        <div className=\"d-flex justify-content-center align-items-center\">\n                          <FaInfinity className=\"text-muted me-2\" />\n                          Full lifetime access\n                        </div>\n\n                        <div className=\"course-includes\">\n                          <h4 className=\"h6 mb-3\">This course includes:</h4>\n                          {hasModules ? (\n                            <ul className=\"list-unstyled\">\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:menu-book\" className=\"text-muted me-2\" />\n                                {courseData?.courseMeta?.modulesCount || 0} Modules\n                              </li>\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <FaVideo className=\"text-muted me-2\" />\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Video').length || 0} Videos • {courseData?.courseMeta?.totalVideoDuration || '00:00:00'} total duration\n                              </li>\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:quiz\" className=\"text-muted me-2\" />\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Assessment').length || 0} Assessments\n                              </li>\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:description-outline\" className=\"text-muted me-2\" />\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Document').length || 0} Documents\n                              </li>\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:analytics-outline\" className=\"text-muted me-2\" />\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Survey').length || 0} Surveys\n                              </li>\n                              {/* <li className=\"mb-2 d-flex align-items-center\">\n                                <Icon icon=\"material-symbols:group\" className=\"text-muted me-2\" />\n                                {courseData?.courseMeta?.enrolledCount || 0} Total Enrollments\n                              </li> */}\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <FaInfinity className=\"text-muted me-2\" />\n                                Full lifetime access\n                              </li>\n                              <li className=\"mb-2 d-flex align-items-center\">\n                                <FaCertificate className=\"text-muted me-2\" />\n                                Certificate of completion\n                              </li>\n                            </ul>\n                          ) : (\n                            <p className=\"text-muted\">Course content is being prepared</p>\n                          )}\n                        </div>\n\n\n\n                      </div>\n                    </div>\n                  </div>\n\n\n                </div>\n              </div>\n            </div>\n\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n\nexport default CourseDetails;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,UAAU,EAC1DC,SAAS,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,MAAM,QACpD,gBAAgB;AACvB,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,SAAS,MAAM,wCAAwC;AAC9D,OAAO,kCAAkC;AACzC,OAAOC,cAAc,MAAM,iDAAiD;AAC5E,SAASC,UAAU,EAAEC,UAAU,QAAQ,6BAA6B;AACpE,SAASC,sBAAsB,EAAEC,YAAY,QAAQ,4BAA4B;AACjF,OAAOC,MAAM,MAAM,gCAAgC;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EACtC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ;MACE,OAAO,GAAG;IAAE;EAChB;AACF,CAAC;AAED,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB;AAE/C,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAGvB,MAAMC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnE,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgE,KAAK,EAAEC,QAAQ,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAGxC,MAAM;IAAEkE;EAAU,CAAC,GAAGrD,SAAS,CAAC,CAAC;EACjC,MAAMsD,OAAO,GAAGjD,UAAU,CAACgD,SAAS,CAAC;EACrC,MAAME,QAAQ,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,EAAE;EAE5BpE,SAAS,CAAC,MAAM;IACdqE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEH,QAAQ,CAAC;EAC7C,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEdnE,SAAS,CAAC,MAAM;IACd,IAAImE,QAAQ,EAAE;MACZI,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLP,QAAQ,CAAC,oBAAoB,CAAC;MAC9BF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,QAAQ,CAAC,CAAC;;EAGd;EACA,MAAMK,iBAAiB,GAAIC,IAAI,IAAK;IAClC,IAAI,CAACA,IAAI,EAAE,OAAOA,IAAI;IAEtB,MAAMC,SAAS,GAAG;MAAE,GAAGD;IAAK,CAAC;;IAE7B;IACA,IAAI,OAAOC,SAAS,CAACC,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI;QACFD,SAAS,CAACC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAACC,IAAI,CAAC;QAC3CN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEI,SAAS,CAACC,IAAI,CAAC;MAC7C,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CW,SAAS,CAACC,IAAI,GAAG,EAAE;MACrB;IACF;;IAEA;IACA,IAAI,OAAOD,SAAS,CAACI,WAAW,KAAK,QAAQ,EAAE;MAC7C,IAAI;QACFJ,SAAS,CAACI,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACH,SAAS,CAACI,WAAW,CAAC;QACzDT,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEI,SAAS,CAACI,WAAW,CAAC;MAC3D,CAAC,CAAC,OAAOf,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDW,SAAS,CAACI,WAAW,GAAG,EAAE;MAC5B;IACF;IAEA,OAAOJ,SAAS;EAClB,CAAC;EAED,eAAeH,gBAAgBA,CAAA,EAAG;IAChC,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChBO,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,MAAMS,QAAQ,GAAG,MAAM5D,sBAAsB,CAAC;QAC5C6D,SAAS,EAAEb,QAAQ;QACnBtC,MAAM,EAAGoD,MAAM,CAACC,QAAQ,CAACC;MAC3B,CAAC,CAAC;MACFd,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAES,QAAQ,CAACN,IAAI,CAAC;MAE9E,IAAIM,QAAQ,CAACK,OAAO,EAAE;QACpB;QACA,MAAMC,aAAa,GAAGb,iBAAiB,CAACO,QAAQ,CAACN,IAAI,CAAC;QACtDJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEe,aAAa,CAAC;QACpDzB,aAAa,CAACyB,aAAa,CAAC;MAC9B,CAAC,MAAM;QACLrB,QAAQ,CAAC,sBAAsB,CAAC;MAClC;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd;MACA,IAAIA,KAAK,CAACgB,QAAQ,IAAIhB,KAAK,CAACgB,QAAQ,CAACO,MAAM,KAAK,GAAG,EAAE;QACnDtB,QAAQ,CAAC,sBAAsB,CAAC;MAClC,CAAC,MAAM,IAAID,KAAK,CAACgB,QAAQ,IAAIhB,KAAK,CAACgB,QAAQ,CAACN,IAAI,IAAIV,KAAK,CAACgB,QAAQ,CAACN,IAAI,CAACc,OAAO,KAAK,8BAA8B,EAAE;QAClHvB,QAAQ,CAAC,sBAAsB,CAAC;MAClC,CAAC,MAAM;QACLK,OAAO,CAACN,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDC,QAAQ,CAAC,uBAAuB,CAAC;MACnC;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF;EAGA,eAAe0B,gBAAgBA,CAAA,EAAG;IAChC,IAAI3D,MAAM,KAAK,YAAY,EAAE;MAC3BoD,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,qBAAqB;IAC9C,CAAC,MAAM,IAAI5D,MAAM,KAAK,sBAAsB,EAAE;MAC5CoD,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,+BAA+B;IACxD,CAAC,MAAM,IAAI5D,MAAM,KAAK,0BAA0B,EAAE;MAChDoD,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,mCAAmC;IAC5D,CAAC,MAAM;MACL;MACApB,OAAO,CAACqB,IAAI,CAAC,8CAA8C,CAAC;MAC5DT,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,qBAAqB;IAC9C;EACF;EAEA,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI9D,MAAM,KAAK,YAAY,EAAE;MAC3BoD,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,qBAAqB;IAC9C,CAAC,MAAM,IAAI5D,MAAM,KAAK,sBAAsB,EAAE;MAC5CoD,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,+BAA+B;IACxD,CAAC,MAAM,IAAI5D,MAAM,KAAK,0BAA0B,EAAE;MAChDoD,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,mCAAmC;IAC5D,CAAC,MAAM;MACL;MACAR,MAAM,CAACC,QAAQ,CAACO,IAAI,GAAG,qBAAqB;IAC9C;EACF,CAAC;EAGD,MAAMG,YAAY,GAAIC,QAAQ,IAAK;IACjCvC,kBAAkB,CAACwC,IAAI,IACrBA,IAAI,CAACC,QAAQ,CAACF,QAAQ,CAAC,GACnBC,IAAI,CAACE,MAAM,CAAC5B,EAAE,IAAIA,EAAE,KAAKyB,QAAQ,CAAC,GAClC,CAAC,GAAGC,IAAI,EAAED,QAAQ,CACxB,CAAC;EACH,CAAC;;EAED;EACA,MAAMI,aAAa,GAAG,CACpB;IACE7B,EAAE,EAAE,SAAS;IACb8B,KAAK,EAAE,uCAAuC;IAC9CC,QAAQ,EAAE,CACR;MAAED,KAAK,EAAE,kBAAkB;MAAEE,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC,EAC/D;MAAEH,KAAK,EAAE,oBAAoB;MAAEE,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAK,CAAC,EACjE;MAAEH,KAAK,EAAE,0BAA0B;MAAEE,QAAQ,EAAE,OAAO;MAAEC,OAAO,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,EACrF;MAAEJ,KAAK,EAAE,+CAA+C;MAAEE,QAAQ,EAAE,OAAO;MAAEE,MAAM,EAAE;IAAK,CAAC,CAC5F;IACDC,YAAY,EAAE,YAAY;IAC1BH,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhC,EAAE,EAAE,SAAS;IACb8B,KAAK,EAAE,uCAAuC;IAC9CK,YAAY,EAAE,YAAY;IAC1BH,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhC,EAAE,EAAE,SAAS;IACb8B,KAAK,EAAE,0BAA0B;IACjCK,YAAY,EAAE,YAAY;IAC1BH,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhC,EAAE,EAAE,SAAS;IACb8B,KAAK,EAAE,uBAAuB;IAC9BK,YAAY,EAAE,YAAY;IAC1BH,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpD,QAAQ,CAAC,yCAAyC,CAAC;EACrD,CAAC;EAED,IAAIS,OAAO,EAAE;IACX,oBACEtC,OAAA;MAAKkF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BnF,OAAA;QAAKkF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnF,OAAA;YAAKkF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnF,OAAA;cAAKkF,SAAS,EAAC,6BAA6B;cAACE,IAAI,EAAC,QAAQ;cAAAD,QAAA,eACxDnF,OAAA;gBAAMkF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNxF,OAAA;cAAGkF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIhD,KAAK,EAAE;IACT,oBACExC,OAAA;MAAKkF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BnF,OAAA;QAAKkF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnF,OAAA;YAAKkF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B3C,KAAK,KAAK,sBAAsB,gBAC/BxC,OAAA,CAAAE,SAAA;cAAAiF,QAAA,gBACEnF,OAAA,CAACT,IAAI;gBAACkG,IAAI,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/ExF,OAAA;gBAAIkF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDxF,OAAA;gBAAGkF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAA6D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3FxF,OAAA;gBACEkF,SAAS,EAAC,sBAAsB;gBAChCW,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,GAAG,CAAE;gBAAAsD,QAAA,gBAE7BnF,OAAA,CAACT,IAAI;kBAACkG,IAAI,EAAC,UAAU;kBAACP,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,eACT,CAAC,gBAEHxF,OAAA,CAAAE,SAAA;cAAAiF,QAAA,gBACEnF,OAAA,CAACT,IAAI;gBAACkG,IAAI,EAAC,0BAA0B;gBAACC,KAAK,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvFxF,OAAA;gBAAIkF,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3CxF,OAAA;gBAAGkF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE3C;cAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,eACrC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACpD,UAAU,EAAE;IACf,oBACEpC,OAAA;MAAKkF,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BnF,OAAA;QAAKkF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnF,OAAA;UAAKkF,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBnF,OAAA;YAAKkF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnF,OAAA,CAACT,IAAI;cAACkG,IAAI,EAAC,kBAAkB;cAACC,KAAK,EAAE;gBAAEC,QAAQ,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/ExF,OAAA;cAAIkF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDxF,OAAA;cAAGkF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAgE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMM,gBAAgB,GAAG1D,UAAU,CAAC2D,kBAAkB,IACpD,CAAC,MAAM;IACL,IAAI;MACF,MAAMC,MAAM,GAAG3C,IAAI,CAACC,KAAK,CAAClB,UAAU,CAAC2D,kBAAkB,CAAC;MACxD,OAAOE,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIA,MAAM,CAACG,MAAM,GAAG,CAAC;IACnD,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVtD,OAAO,CAACN,KAAK,CAAC,8BAA8B,EAAE4D,CAAC,CAAC;MAChD,OAAO,KAAK;IACd;EACF,CAAC,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAGjE,UAAU,CAACkE,OAAO,IACnCC,MAAM,CAACC,IAAI,CAACpE,UAAU,CAACkE,OAAO,CAAC,CAACH,MAAM,GAAG,CAAC;EAE5C,oBACEnG,OAAA,CAAAE,SAAA;IAAAiF,QAAA,eACEnF,OAAA;MAAKkF,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBnF,OAAA;QAAKkF,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBnF,OAAA;UAAKkF,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzCnF,OAAA;YAAKkF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAC1BnF,OAAA;cACVkF,SAAS,EAAC,cAAc;cACxBQ,KAAK,EAAE;gBACLe,QAAQ,EAAE,YAAY;gBACtBC,YAAY,EAAE,YAAY;gBAC1BC,UAAU,EAAE;cACd,CAAE;cAAAxB,QAAA,EAED/C,UAAU,CAACwE,WAAW,IAAI;YAAiB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACSxF,OAAA;cAAGkF,SAAS,EAAC,aAAa;cAAAC,QAAA,EACvB/C,UAAU,CAACgB,IAAI,IAAI6C,KAAK,CAACC,OAAO,CAAC9D,UAAU,CAACgB,IAAI,CAAC,GAChDhB,UAAU,CAACgB,IAAI,CAACyD,IAAI,CAAC,KAAK,CAAC,GAE3B;YACD;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACJxF,OAAA;cAAKkF,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC7DnF,OAAA;gBAAKkF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnF,OAAA,CAACT,IAAI;kBAACkG,IAAI,EAAC,uBAAuB;kBAACP,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9DxF,OAAA;kBAAMkF,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAE,CAAA/C,UAAU,aAAVA,UAAU,wBAAAxB,qBAAA,GAAVwB,UAAU,CAAE0E,UAAU,cAAAlG,qBAAA,uBAAtBA,qBAAA,CAAwBmG,aAAa,KAAI,KAAK,EAAC,SAAO;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACNxF,OAAA;gBAAKkF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnF,OAAA,CAACT,IAAI;kBAACkG,IAAI,EAAC,4BAA4B;kBAACP,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DxF,OAAA;kBAAAmF,QAAA,GAAO,CAAA/C,UAAU,aAAVA,UAAU,wBAAAvB,sBAAA,GAAVuB,UAAU,CAAE0E,UAAU,cAAAjG,sBAAA,uBAAtBA,sBAAA,CAAwBmG,YAAY,KAAI,CAAC,EAAC,UAAQ;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eAKNxF,OAAA;gBAAKkF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnF,OAAA,CAACT,IAAI;kBAACkG,IAAI,EAAC,yBAAyB;kBAACP,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDxF,OAAA;kBAAAmF,QAAA,GAAM,YAAU,EAAC,CAAA/C,UAAU,aAAVA,UAAU,wBAAAtB,sBAAA,GAAVsB,UAAU,CAAE0E,UAAU,cAAAhG,sBAAA,uBAAtBA,sBAAA,CAAwBmG,kBAAkB,KAAI,UAAU;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNxF,OAAA;gBAAKkF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnF,OAAA,CAACT,IAAI;kBAACkG,IAAI,EAAC,yBAAyB;kBAACP,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDxF,OAAA;kBAAAmF,QAAA,GAAM,SAAO,EAAC,CAAA/C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE8E,MAAM,KAAI,eAAe;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNxF,OAAA;gBAAKkF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCnF,OAAA,CAACT,IAAI;kBAACkG,IAAI,EAAC,2BAA2B;kBAACP,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1DxF,OAAA;kBAAAmF,QAAA,GAAM,YAAU,EAAC,CAAA/C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE+E,eAAe,KAAI,eAAe;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxF,OAAA;cAAKkF,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDnF,OAAA;gBAAKkF,SAAS,EAAC,kBAAkB;gBAACQ,KAAK,EAAE;kBAAE0B,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAnC,QAAA,eAC3FnF,OAAA;kBACEuH,GAAG,EAAE,CAAAnF,UAAU,aAAVA,UAAU,wBAAArB,mBAAA,GAAVqB,UAAU,CAAEoF,OAAO,cAAAzG,mBAAA,uBAAnBA,mBAAA,CAAqB0G,OAAO,KAAIhI,cAAe;kBACpDiI,GAAG,EAAEtF,UAAU,aAAVA,UAAU,wBAAApB,oBAAA,GAAVoB,UAAU,CAAEoF,OAAO,cAAAxG,oBAAA,uBAAnBA,oBAAA,CAAqB2G,IAAK;kBAC/BzC,SAAS,EAAC,gBAAgB;kBAC1BQ,KAAK,EAAE;oBAAE0B,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEO,SAAS,EAAE;kBAAQ;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNxF,OAAA;gBAAKkF,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjCnF,OAAA;kBAAMkF,SAAS,EAAC,aAAa;kBAAAC,QAAA,GAAC,KAAG,EAAC,CAAA/C,UAAU,aAAVA,UAAU,wBAAAnB,oBAAA,GAAVmB,UAAU,CAAEoF,OAAO,cAAAvG,oBAAA,uBAAnBA,oBAAA,CAAqB0G,IAAI,KAAI,YAAY;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnFxF,OAAA;kBAAMkF,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE,CAAA/C,UAAU,aAAVA,UAAU,wBAAAlB,oBAAA,GAAVkB,UAAU,CAAEoF,OAAO,cAAAtG,oBAAA,uBAAnBA,oBAAA,CAAqBkE,IAAI,KAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAKkF,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzCnF,OAAA;YAAKkF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BnF,OAAA;cAAKkF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCnF,OAAA;gBAAKkF,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBnF,OAAA;kBAAKkF,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BnF,OAAA;oBAAKkF,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpCnF,OAAA;sBAAIkF,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxCxF,OAAA;sBAAKkF,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,EAC7B/C,UAAU,CAACmB,WAAW,IAAI0C,KAAK,CAACC,OAAO,CAAC9D,UAAU,CAACmB,WAAW,CAAC,GAC9DnB,UAAU,CAACmB,WAAW,CAACsE,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACtC/H,OAAA;wBAAiBkF,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,gBACxEnF,OAAA,CAACT,IAAI;0BAACkG,IAAI,EAAC,+BAA+B;0BAACP,SAAS,EAAC;wBAAmB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3ExF,OAAA;0BAAAmF,QAAA,EAAO2C;wBAAK;0BAAAzC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAFZuC,KAAK;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGV,CACN,CAAC,gBAEFxF,OAAA;wBAAKkF,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,gBAC5DnF,OAAA,CAACT,IAAI;0BAACkG,IAAI,EAAC,+BAA+B;0BAACP,SAAS,EAAC;wBAAmB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3ExF,OAAA;0BAAAmF,QAAA,EAAM;wBAAgC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELpD,UAAU,CAAC4F,WAAW,gBACrBhI,OAAA;oBAAKkF,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCnF,OAAA;sBAAIkF,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9CxF,OAAA;sBAAKkF,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5BnF,OAAA;wBACEkF,SAAS,EAAC,iBAAiB;wBAC3BQ,KAAK,EAAE;0BACLuC,SAAS,EAAE,YAAY;0BACvBtB,UAAU,EAAE;wBACd,CAAE;wBAAAxB,QAAA,EAED/C,UAAU,CAAC4F;sBAAW;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACJ,IAAI,eAGRxF,OAAA;oBAAKkF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAC9CnF,OAAA;sBAAIkF,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC1Ca,UAAU,gBACTrG,OAAA;sBAAKkF,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAC1BoB,MAAM,CAAC2B,OAAO,CAAC9F,UAAU,CAACkE,OAAO,CAAC,CAACuB,GAAG,CAAC,CAAC,CAACM,UAAU,EAAEC,QAAQ,CAAC,kBAC7DpI,OAAA;wBAAsBkF,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC3CnF,OAAA;0BACEkF,SAAS,EAAC,qEAAqE;0BAC/EW,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC8D,UAAU,CAAE;0BACxCzC,KAAK,EAAE;4BAAE2C,MAAM,EAAE;0BAAU,CAAE;0BAAAlD,QAAA,gBAE7BnF,OAAA;4BAAKkF,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,GACvCrD,eAAe,CAAC0C,QAAQ,CAAC2D,UAAU,CAAC,gBAAGnI,OAAA,CAACf,aAAa;8BAAAoG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACd,WAAW;8BAAAmG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAC3ExF,OAAA;8BAAMkF,SAAS,EAAC,MAAM;8BAAAC,QAAA,EAAEgD;4BAAU;8BAAA9C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC,eACNxF,OAAA;4BAAKkF,SAAS,EAAC,wBAAwB;4BAAAC,QAAA,eACrCnF,OAAA;8BAAAmF,QAAA,GAAQiD,QAAQ,CAACjC,MAAM,EAAC,QAAM;4BAAA;8BAAAd,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EACL1D,eAAe,CAAC0C,QAAQ,CAAC2D,UAAU,CAAC,iBACnCnI,OAAA;0BAAKkF,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,EAC5BiD,QAAQ,CAACP,GAAG,CAAC,CAACS,OAAO,EAAEC,GAAG,kBACzBvI,OAAA;4BAAekF,SAAS,EAAC,oEAAoE;4BAAAC,QAAA,gBAC3FnF,OAAA;8BAAKkF,SAAS,EAAC,2BAA2B;8BAAAC,QAAA,GACvCmD,OAAO,CAACE,IAAI,KAAK,OAAO,iBACvBxI,OAAA,CAACT,IAAI;gCAACkG,IAAI,EAAC,sCAAsC;gCAACP,SAAS,EAAC;8BAAM;gCAAAG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CACrE,EACA8C,OAAO,CAACE,IAAI,KAAK,UAAU,iBAC1BxI,OAAA,CAACT,IAAI;gCAACkG,IAAI,EAAC,sCAAsC;gCAACP,SAAS,EAAC;8BAAM;gCAAAG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CACrE,EACA8C,OAAO,CAACE,IAAI,KAAK,YAAY,iBAC5BxI,OAAA,CAACT,IAAI;gCAACkG,IAAI,EAAC,+BAA+B;gCAACP,SAAS,EAAC;8BAAM;gCAAAG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAC9D,EACA8C,OAAO,CAACE,IAAI,KAAK,QAAQ,iBACxBxI,OAAA,CAACT,IAAI;gCAACkG,IAAI,EAAC,oCAAoC;gCAACP,SAAS,EAAC;8BAAM;gCAAAG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CACnE,eACDxF,OAAA;gCAAAmF,QAAA,EAAOmD,OAAO,CAAC3D;8BAAK;gCAAAU,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzB,CAAC,eACNxF,OAAA;8BAAKkF,SAAS,EAAC,2BAA2B;8BAAAC,QAAA,GACvCmD,OAAO,CAACE,IAAI,KAAK,OAAO,iBACvBxI,OAAA;gCAAMkF,SAAS,EAAC,iBAAiB;gCAAAC,QAAA,EAAEmD,OAAO,CAACzD;8BAAQ;gCAAAQ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC3D,EACA8C,OAAO,CAACE,IAAI,KAAK,UAAU,iBAC1BxI,OAAA;gCAAMkF,SAAS,EAAC,iBAAiB;gCAAAC,QAAA,EAAEmD,OAAO,CAACG;8BAAQ;gCAAApD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC3D,EACA8C,OAAO,CAACE,IAAI,KAAK,YAAY,IAAIF,OAAO,CAACI,SAAS,iBACjD1I,OAAA;gCAAMkF,SAAS,EAAC,iBAAiB;gCAAAC,QAAA,EAAEmD,OAAO,CAACI;8BAAS;gCAAArD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC5D;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA,GA1BE+C,GAAG;4BAAAlD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA2BR,CACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;sBAAA,GA/CO2C,UAAU;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiDf,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,gBAENxF,OAAA,CAACF,MAAM;sBAAC6I,OAAO,EAAC;oBAAiC;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CACpD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAELpD,UAAU,CAACoF,OAAO,gBACjBxH,OAAA;oBAAKkF,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCnF,OAAA;sBAAIkF,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvCxF,OAAA;sBAAKkF,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,gBAGnDnF,OAAA;wBACEkF,SAAS,EAAC,uCAAuC;wBACjDQ,KAAK,EAAE;0BACL0B,KAAK,EAAE,OAAO;0BACdC,MAAM,EAAE,OAAO;0BACfC,QAAQ,EAAE,OAAO;0BACjBsB,QAAQ,EAAE,QAAQ;0BAClBC,YAAY,EAAE,MAAM;0BACpBC,QAAQ,EAAE,UAAU;0BACpBC,eAAe,EAAE;wBACnB,CAAE;wBAAA5D,QAAA,eAEFnF,OAAA;0BACEuH,GAAG,EAAE,CAAAnF,UAAU,aAAVA,UAAU,wBAAAjB,oBAAA,GAAViB,UAAU,CAAEoF,OAAO,cAAArG,oBAAA,uBAAnBA,oBAAA,CAAqBsG,OAAO,KAAIhI,cAAe;0BACpDiI,GAAG,EAAEtF,UAAU,aAAVA,UAAU,wBAAAhB,oBAAA,GAAVgB,UAAU,CAAEoF,OAAO,cAAApG,oBAAA,uBAAnBA,oBAAA,CAAqBuG,IAAK;0BAC/BjC,KAAK,EAAE;4BACL0B,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdO,SAAS,EAAE;0BACb;wBAAE;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eAGNxF,OAAA;wBAAKkF,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,gBAC9BnF,OAAA;0BAAIkF,SAAS,EAAC,SAAS;0BAACQ,KAAK,EAAE;4BAACsD,SAAS,EAAE;0BAAM,CAAE;0BAAA7D,QAAA,EAAE,CAAA/C,UAAU,aAAVA,UAAU,wBAAAf,oBAAA,GAAVe,UAAU,CAAEoF,OAAO,cAAAnG,oBAAA,uBAAnBA,oBAAA,CAAqBsG,IAAI,KAAI;wBAAY;0BAAAtC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACpGxF,OAAA;0BAAGkF,SAAS,EAAC,iBAAiB;0BAACQ,KAAK,EAAE;4BAACsD,SAAS,EAAE;0BAAM,CAAE;0BAAA7D,QAAA,EACvD,CAAA/C,UAAU,aAAVA,UAAU,wBAAAd,oBAAA,GAAVc,UAAU,CAAEoF,OAAO,cAAAlG,oBAAA,uBAAnBA,oBAAA,CAAqB8D,IAAI,KAAI;wBAAoD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjF,CAAC,eA2BJxF,OAAA;0BAAGkF,SAAS,EAAC,iBAAiB;0BAACQ,KAAK,EAAE;4BAACsD,SAAS,EAAC;0BAAM,CAAE;0BAAA7D,QAAA,EACtD,CAAA/C,UAAU,aAAVA,UAAU,wBAAAb,oBAAA,GAAVa,UAAU,CAAEoF,OAAO,cAAAjG,oBAAA,uBAAnBA,oBAAA,CAAqB0H,GAAG,KAAI;wBAAkB;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAINxF,OAAA,CAACF,MAAM;oBAAC6I,OAAO,EAAC;kBAAsC;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACzD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENxF,OAAA;kBAAKkF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,eACpCnF,OAAA;oBAAKkF,SAAS,EAAC,4BAA4B;oBAACQ,KAAK,EAAE;sBAAEoD,QAAQ,EAAE,UAAU;sBAAEI,GAAG,EAAE;oBAAQ,CAAE;oBAAA/D,QAAA,gBACxFnF,OAAA;sBACEuH,GAAG,EAAEnF,UAAU,CAAC+G,YAAY,IAAI3J,SAAU;sBAC1C0F,SAAS,EAAC,wBAAwB;sBAClCwC,GAAG,EAAEtF,UAAU,CAACwE,WAAW,IAAI,gBAAiB;sBAChDwC,OAAO,EAAGhD,CAAC,IAAK;wBACdA,CAAC,CAACiD,MAAM,CAAC9B,GAAG,GAAG/H,SAAS;wBACxB4G,CAAC,CAACiD,MAAM,CAACC,OAAO,GAAG,IAAI;sBACzB;oBAAE;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFxF,OAAA;sBAAKkF,SAAS,EAAC,WAAW;sBAAAC,QAAA,gBACxBnF,OAAA;wBAAKkF,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,gBACrEnF,OAAA;0BAAMkF,SAAS,EAAC,WAAW;0BAAAC,QAAA,GAAEhF,iBAAiB,CAACiC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhC,QAAQ,CAAC,EAAC,GAAC,EAAC,CAAAgC,UAAU,aAAVA,UAAU,wBAAAZ,qBAAA,GAAVY,UAAU,CAAEmH,WAAW,cAAA/H,qBAAA,uBAAvBA,qBAAA,CAAyBgI,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,GAAG,GAAG,CAAApH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqH,YAAY,KAAI,GAAG;wBAAA;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACvK,CAAApD,UAAU,aAAVA,UAAU,wBAAAX,sBAAA,GAAVW,UAAU,CAAEmH,WAAW,cAAA9H,sBAAA,uBAAvBA,sBAAA,CAAyB+H,WAAW,CAAC,CAAC,MAAK,MAAM,gBAChDxJ,OAAA;0BAAMkF,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,GAAEhF,iBAAiB,CAACiC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhC,QAAQ,CAAC,EAAC,GAAC;wBAAA;0BAAAiF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,gBAElFxF,OAAA;0BAAMkF,SAAS,EAAC,gBAAgB;0BAAAC,QAAA,GAAEhF,iBAAiB,CAACiC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEhC,QAAQ,CAAC,EAAEsJ,MAAM,CAAC,CAAAtH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqH,YAAY,KAAI,CAAC,CAAC,GAAG,CAAC;wBAAA;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAC5H;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAEL,CAAApD,UAAU,aAAVA,UAAU,wBAAAV,sBAAA,GAAVU,UAAU,CAAEmH,WAAW,cAAA7H,sBAAA,uBAAvBA,sBAAA,CAAyB8H,WAAW,CAAC,CAAC,MAAK,MAAM,gBAChDxJ,OAAA;wBACE6F,OAAO,EAAEzB,oBAAqB;wBAC9Bc,SAAS,EAAC,UAAU;wBACpByE,QAAQ,EAAEzH,eAAgB;wBAAAiD,QAAA,EAEzBjD,eAAe,gBACdlC,OAAA,CAAAE,SAAA;0BAAAiF,QAAA,gBACEnF,OAAA;4BAAMkF,SAAS,EAAC,uCAAuC;4BAACE,IAAI,EAAC,QAAQ;4BAAC,eAAY;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,iBAElG;wBAAA,eAAE,CAAC,gBAEHxF,OAAA,CAAAE,SAAA;0BAAAiF,QAAA,gBACEnF,OAAA,CAACT,IAAI;4BACHkG,IAAI,EAAC,UAAU;4BACfP,SAAS,EAAC;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC,cAEJ;wBAAA,eAAE;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC,gBAETxF,OAAA;wBACEkF,SAAS,EAAC,wBAAwB;wBAClCW,OAAO,EAAE5B,gBAAiB;wBAC1B0F,QAAQ,EAAE3H,WAAY;wBAAAmD,QAAA,EAErBnD,WAAW,gBACVhC,OAAA,CAAAE,SAAA;0BAAAiF,QAAA,gBACEnF,OAAA;4BAAMkF,SAAS,EAAC,uCAAuC;4BAACE,IAAI,EAAC,QAAQ;4BAAC,eAAY;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,gBAElG;wBAAA,eAAE,CAAC,gBAEHxF,OAAA,CAAAE,SAAA;0BAAAiF,QAAA,gBACEnF,OAAA,CAACT,IAAI;4BACHkG,IAAI,EAAC,iBAAiB;4BACtBP,SAAS,EAAC;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC,EACDpD,UAAU,CAACwH,kBAAkB,GAAG,mBAAmB,GAAG,WAAW;wBAAA,eAClE;sBACH;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CACT,eAGDxF,OAAA;wBAAKkF,SAAS,EAAC,kDAAkD;wBAAAC,QAAA,gBAC/DnF,OAAA,CAACpB,UAAU;0BAACsG,SAAS,EAAC;wBAAiB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,wBAE5C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENxF,OAAA;wBAAKkF,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,gBAC9BnF,OAAA;0BAAIkF,SAAS,EAAC,SAAS;0BAAAC,QAAA,EAAC;wBAAqB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACjDa,UAAU,gBACTrG,OAAA;0BAAIkF,SAAS,EAAC,eAAe;0BAAAC,QAAA,gBAC3BnF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACT,IAAI;8BAACkG,IAAI,EAAC,4BAA4B;8BAACP,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACrE,CAAApD,UAAU,aAAVA,UAAU,wBAAAT,sBAAA,GAAVS,UAAU,CAAE0E,UAAU,cAAAnF,sBAAA,uBAAtBA,sBAAA,CAAwBqF,YAAY,KAAI,CAAC,EAAC,UAC7C;0BAAA;4BAAA3B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLxF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACtB,OAAO;8BAACwG,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACtC,CAAApD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkE,OAAO,KAAIC,MAAM,CAACsD,MAAM,CAACzH,UAAU,CAACkE,OAAO,CAAC,CAACwD,IAAI,CAAC,CAAC,CAACrF,MAAM,CAACsF,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,OAAO,CAAC,CAACrC,MAAM,IAAI,CAAC,EAAC,iBAAU,EAAC,CAAA/D,UAAU,aAAVA,UAAU,wBAAAR,sBAAA,GAAVQ,UAAU,CAAE0E,UAAU,cAAAlF,sBAAA,uBAAtBA,sBAAA,CAAwBqF,kBAAkB,KAAI,UAAU,EAAC,iBACzL;0BAAA;4BAAA5B,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLxF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACT,IAAI;8BAACkG,IAAI,EAAC,uBAAuB;8BAACP,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAChE,CAAApD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkE,OAAO,KAAIC,MAAM,CAACsD,MAAM,CAACzH,UAAU,CAACkE,OAAO,CAAC,CAACwD,IAAI,CAAC,CAAC,CAACrF,MAAM,CAACsF,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,YAAY,CAAC,CAACrC,MAAM,IAAI,CAAC,EAAC,cAC1H;0BAAA;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLxF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACT,IAAI;8BAACkG,IAAI,EAAC,sCAAsC;8BAACP,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC/E,CAAApD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkE,OAAO,KAAIC,MAAM,CAACsD,MAAM,CAACzH,UAAU,CAACkE,OAAO,CAAC,CAACwD,IAAI,CAAC,CAAC,CAACrF,MAAM,CAACsF,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,UAAU,CAAC,CAACrC,MAAM,IAAI,CAAC,EAAC,YACxH;0BAAA;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLxF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACT,IAAI;8BAACkG,IAAI,EAAC,oCAAoC;8BAACP,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC7E,CAAApD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkE,OAAO,KAAIC,MAAM,CAACsD,MAAM,CAACzH,UAAU,CAACkE,OAAO,CAAC,CAACwD,IAAI,CAAC,CAAC,CAACrF,MAAM,CAACsF,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,QAAQ,CAAC,CAACrC,MAAM,IAAI,CAAC,EAAC,UACtH;0BAAA;4BAAAd,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAKLxF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACpB,UAAU;8BAACsG,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,wBAE5C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLxF,OAAA;4BAAIkF,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,gBAC5CnF,OAAA,CAACnB,aAAa;8BAACqG,SAAS,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,6BAE/C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAELxF,OAAA;0BAAGkF,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAgC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAC9D;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAIH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACN,CAAC;AAEP;AAAC7E,EAAA,CA9mBQD,aAAa;EAAA,QAGHtB,WAAW,EASNC,SAAS;AAAA;AAAA2K,EAAA,GAZxBtJ,aAAa;AAgnBtB,eAAeA,aAAa;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}