{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\feed\\\\MyFeed.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Icon } from '@iconify/react';\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png';\nimport FeedPost from './FeedPost';\nimport { getMyPosts, toggleLike, addComment, getPostComments, editComment, deleteComment, generatePostShareUrl } from '../../../services/feedServices';\nimport { toast } from 'react-toastify';\n\n// Move ActionButton outside to prevent recreation on every render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ActionButton = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  icon,\n  count,\n  onClick,\n  isLiked,\n  isLast,\n  buttonStyle,\n  actionButtonStyle\n}) => {\n  _s();\n  const buttonClass = useMemo(() => `btn border ${isLiked ? 'text-danger' : 'text-muted'}`, [isLiked]);\n  const buttonStyleMemo = useMemo(() => isLast ? {\n    ...actionButtonStyle,\n    marginRight: 0\n  } : actionButtonStyle, [isLast, actionButtonStyle]);\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: buttonClass,\n    onClick: onClick,\n    style: buttonStyleMemo,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center justify-content-center\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        icon: icon,\n        style: {\n          fontSize: '1.2rem'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), count && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"ms-1\",\n        style: {\n          fontSize: '0.9rem'\n        },\n        children: count\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 19\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}, \"BW3KalXNlo6xULmJhXkh9aylRuM=\")), \"BW3KalXNlo6xULmJhXkh9aylRuM=\");\n_c2 = ActionButton;\nconst MyFeed = () => {\n  _s2();\n  // Posts state\n  const [posts, setPosts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [loadingMore, setLoadingMore] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const [postingNewPost, setPostingNewPost] = useState(false);\n  const [userProfile, setUserProfile] = useState(null);\n\n  // Comments state\n  const [newComment, setNewComment] = useState({});\n  const [showComments, setShowComments] = useState({});\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\n  const [editingComment, setEditingComment] = useState({}); // Track which comment is being edited\n  const [editCommentText, setEditCommentText] = useState({}); // Store edit text for each comment\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\n\n  const user_id = JSON.parse(localStorage.getItem('user')).id;\n\n  // Load my posts\n  const loadMyPosts = useCallback(async (page = 1, append = false) => {\n    try {\n      if (page === 1) {\n        setLoading(true);\n      } else {\n        setLoadingMore(true);\n      }\n      const response = await getMyPosts(page, 5);\n      if (response.success) {\n        const transformedPosts = response.data.posts.map(post => ({\n          id: post.id,\n          user: {\n            name: post.user_name,\n            avatar: post.user_avatar || DefaultProfile\n          },\n          content: post.description,\n          media: post.media_url ? {\n            type: post.media_type,\n            url: post.media_url\n          } : null,\n          isLiked: post.is_liked_by_user === 1,\n          likes: post.likes_count,\n          commentsCount: post.comments_count,\n          created_at: post.created_at\n        }));\n        if (append) {\n          setPosts(prev => [...prev, ...transformedPosts]);\n        } else {\n          setPosts(transformedPosts);\n          // Store user profile from first post\n          if (transformedPosts.length > 0) {\n            setUserProfile({\n              name: transformedPosts[0].user.name,\n              profile_pic_url: transformedPosts[0].user.avatar\n            });\n          }\n        }\n        setCurrentPage(page);\n        setHasMore(response.data.pagination.has_more);\n      } else {\n        toast.error('Failed to load posts');\n      }\n    } catch (error) {\n      console.error('Error loading posts:', error);\n      toast.error('Failed to load posts');\n    } finally {\n      setLoading(false);\n      setLoadingMore(false);\n    }\n  }, []);\n\n  // Load initial posts\n  useEffect(() => {\n    loadMyPosts(1);\n  }, [loadMyPosts]);\n\n  // Infinite scroll handler\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollTop = document.documentElement.scrollTop;\n      const scrollHeight = document.documentElement.scrollHeight;\n      const clientHeight = document.documentElement.clientHeight;\n\n      // Check if user has scrolled to bottom (with 100px threshold)\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\n        if (!loadingMore && hasMore) {\n          loadMorePosts();\n        }\n      }\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [loadingMore, hasMore]);\n\n  // Button styles\n  // Memoized styles to prevent re-renders\n  const buttonStyle = useMemo(() => ({\n    backgroundColor: 'transparent',\n    borderColor: '#dee2e6'\n  }), []);\n  const actionButtonStyle = useMemo(() => ({\n    flex: 1,\n    marginRight: '10px',\n    ...buttonStyle\n  }), [buttonStyle]);\n\n  // Load comments for a specific post\n  const loadPostComments = useCallback(async postId => {\n    try {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: true\n      }));\n\n      // Load all comments at once by setting a high page size\n      const response = await getPostComments(postId, 1, 1000);\n      if (response.success) {\n        const newComments = response.data.comments.map(comment => ({\n          id: comment.id,\n          user: comment.user_name,\n          user_id: comment.user_id,\n          avatar: comment.user_avatar || DefaultProfile,\n          text: comment.comment,\n          timestamp: new Date(comment.commented_at).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n          })\n        }));\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: newComments\n        }));\n      } else {\n        toast.error('Failed to load comments');\n      }\n    } catch (error) {\n      console.error('Error loading comments:', error);\n      toast.error('Failed to load comments');\n    } finally {\n      setCommentsLoading(prev => ({\n        ...prev,\n        [postId]: false\n      }));\n    }\n  }, []);\n\n  // Event handlers\n  const handleLike = async postId => {\n    try {\n      const response = await toggleLike(postId);\n      if (response.success) {\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          isLiked: !post.isLiked,\n          likes: post.isLiked ? post.likes - 1 : post.likes + 1\n        } : post));\n      } else {\n        toast.error('Failed to update like');\n      }\n    } catch (error) {\n      console.error('Error toggling like:', error);\n      toast.error('Failed to update like');\n    }\n  };\n  const handleComment = postId => {\n    const isOpening = !showComments[postId];\n    setShowComments(prev => ({\n      ...prev,\n      [postId]: !prev[postId]\n    }));\n\n    // Load comments when opening comments section for the first time\n    if (isOpening && !postComments[postId]) {\n      loadPostComments(postId);\n    }\n  };\n  const handleSubmitComment = async postId => {\n    const commentText = newComment[postId];\n    if (!commentText || !commentText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n    if (commentText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await addComment(postId, commentText.trim());\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount + 1\n        } : post));\n\n        // Add the new comment to the comments list\n        const newCommentObj = {\n          id: response.data.comment.id,\n          user: response.data.comment.user_name,\n          user_id: response.data.comment.user_id,\n          avatar: response.data.comment.user_avatar || DefaultProfile,\n          text: response.data.comment.comment,\n          timestamp: 'Just now'\n        };\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\n        }));\n        setNewComment(prev => ({\n          ...prev,\n          [postId]: ''\n        }));\n        toast.success('Comment added successfully');\n      } else {\n        toast.error('Failed to add comment');\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handlePostSubmit = async newPost => {\n    console.log('handlePostSubmit called with:', newPost);\n    setPostingNewPost(true);\n\n    // Simulate API delay and then refresh the feed\n    setTimeout(async () => {\n      try {\n        await loadMyPosts(1); // Reload posts to include the new one\n        toast.success('Post created successfully!');\n      } finally {\n        setPostingNewPost(false);\n      }\n    }, 2000); // 2 second delay\n  };\n  const loadMorePosts = useCallback(() => {\n    if (!loadingMore && hasMore) {\n      console.log('Loading more posts...', {\n        currentPage: currentPage + 1,\n        hasMore\n      });\n      loadMyPosts(currentPage + 1, true);\n    }\n  }, [loadMyPosts, loadingMore, hasMore, currentPage]);\n  const handleShare = async post => {\n    try {\n      // Generate shareable URL for the post\n      const response = await generatePostShareUrl(post.id);\n      if (response.success) {\n        const shareUrl = response.data.shareUrl;\n\n        // Prepare share data\n        const shareData = {\n          title: `${post.user.name}'s Post`,\n          text: post.content || 'Check out this post!',\n          url: shareUrl\n        };\n\n        // Check if Web Share API is supported\n        if (navigator.share) {\n          await navigator.share(shareData);\n          console.log('Shared successfully');\n        } else {\n          // Fallback for browsers that don't support Web Share API\n          // Copy to clipboard\n          await navigator.clipboard.writeText(shareUrl);\n          toast.success('Post link copied to clipboard!');\n        }\n      } else {\n        toast.error('Failed to generate share link');\n      }\n    } catch (error) {\n      console.error('Error sharing post:', error);\n      if (error.name !== 'AbortError') {\n        toast.error('Failed to share post');\n      }\n    }\n  };\n\n  // Render functions\n  const renderMedia = media => {\n    if (!media) return null;\n    if (media.type === 'image') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-relative\",\n        style: {\n          width: '100%',\n          maxHeight: '400px',\n          overflow: 'hidden',\n          borderRadius: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: media.url,\n          className: \"img-fluid\",\n          alt: \"Post media\",\n          style: {\n            width: '100%',\n            height: 'auto',\n            maxHeight: '400px',\n            objectFit: 'contain',\n            display: 'block'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this);\n    } else if (media.type === 'video') {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-relative\",\n        style: {\n          width: '100%',\n          maxHeight: '400px',\n          overflow: 'hidden',\n          borderRadius: '8px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"video\", {\n          className: \"img-fluid\",\n          controls: true,\n          style: {\n            width: '100%',\n            height: 'auto',\n            maxHeight: '400px',\n            objectFit: 'contain',\n            display: 'block'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"source\", {\n            src: media.url,\n            type: \"video/mp4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), \"Your browser does not support the video tag.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderPostContent = (content, post) => {\n    if (!content) return null;\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\n\n    // For text-only posts, show full content\n    if (!hasMedia) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"card-text mb-2\",\n          children: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this);\n    }\n\n    // For posts with media, show truncated text with \"Show more\" option\n    const shouldTruncate = content.length > 100;\n    const isShowingFull = showFullText[post.id];\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"card-text mb-2\",\n        children: [displayText, shouldTruncate && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-link p-0 ms-2 text-primary text-decoration-none\",\n          onClick: () => setShowFullText(prev => ({\n            ...prev,\n            [post.id]: !isShowingFull\n          })),\n          children: isShowingFull ? 'Show less' : 'Show more'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Memoized comment handlers to prevent re-renders\n  const handleCommentChange = useCallback((postId, value) => {\n    setNewComment(prev => ({\n      ...prev,\n      [postId]: value\n    }));\n  }, []);\n  const handleCommentKeyDown = useCallback((e, postId) => {\n    if (e.key === 'Enter') {\n      handleSubmitComment(postId);\n    }\n  }, [handleSubmitComment]);\n  const handleCommentSubmitClick = useCallback(postId => {\n    handleSubmitComment(postId);\n  }, [handleSubmitComment]);\n  const handleEditComment = async (postId, commentId) => {\n    const editText = editCommentText[commentId];\n    if (!editText || !editText.trim()) {\n      toast.error('Please enter a comment');\n      return;\n    }\n    if (editText.length > 400) {\n      toast.error('Comment cannot exceed 400 characters');\n      return;\n    }\n    try {\n      const response = await editComment(commentId, editText.trim());\n      if (response.success) {\n        // Update the comment in the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].map(comment => comment.id === commentId ? {\n            ...comment,\n            text: editText.trim()\n          } : comment)\n        }));\n        setEditingComment(prev => ({\n          ...prev,\n          [commentId]: false\n        }));\n        setEditCommentText(prev => ({\n          ...prev,\n          [commentId]: ''\n        }));\n        toast.success('Comment updated successfully');\n      } else {\n        toast.error('Failed to update comment');\n      }\n    } catch (error) {\n      console.error('Error updating comment:', error);\n      toast.error('Failed to update comment');\n    }\n  };\n  const handleDeleteComment = async (postId, commentId) => {\n    if (!window.confirm('Are you sure you want to delete this comment?')) {\n      return;\n    }\n    try {\n      const response = await deleteComment(commentId);\n      if (response.success) {\n        // Update the post's comments count\n        setPosts(posts.map(post => post.id === postId ? {\n          ...post,\n          commentsCount: post.commentsCount - 1\n        } : post));\n\n        // Remove the comment from the comments list\n        setPostComments(prev => ({\n          ...prev,\n          [postId]: prev[postId].filter(comment => comment.id !== commentId)\n        }));\n        toast.success('Comment deleted successfully');\n      } else {\n        toast.error('Failed to delete comment');\n      }\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n    }\n  };\n  const handleEditCommentChange = useCallback((commentId, value) => {\n    setEditCommentText(prev => ({\n      ...prev,\n      [commentId]: value\n    }));\n  }, []);\n  const handleEditCommentKeyDown = useCallback((e, postId, commentId) => {\n    if (e.key === 'Enter') {\n      handleEditComment(postId, commentId);\n    }\n  }, [handleEditComment]);\n  const renderComments = post => {\n    if (!showComments[post.id]) return null;\n    const comments = postComments[post.id] || [];\n    const isLoading = commentsLoading[post.id];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-top pt-3 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-3\",\n        children: [\"Comments (\", post.commentsCount || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n          className: \"rounded-circle me-2\",\n          alt: \"Profile\",\n          style: {\n            width: '32px',\n            height: '32px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control\",\n            placeholder: \"Write a comment...\",\n            value: newComment[post.id] || '',\n            onChange: e => handleCommentChange(post.id, e.target.value),\n            onKeyDown: e => handleCommentKeyDown(e, post.id),\n            maxLength: 400\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-end mt-1\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: (newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n              children: [(newComment[post.id] || '').length, \"/400 characters\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-sm ms-2 w-auto\",\n          onClick: () => handleCommentSubmitClick(post.id),\n          disabled: !newComment[post.id] || !newComment[post.id].trim(),\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border spinner-border-sm\",\n          role: \"status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading comments...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-muted small\",\n          children: \"Loading comments...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          id: `comments-container-${post.id}`,\n          children: comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: comment.avatar,\n              className: \"rounded-circle me-2\",\n              alt: comment.user,\n              style: {\n                width: '32px',\n                height: '32px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-light rounded p-2 flex-grow-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between align-items-start\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"fw-bold\",\n                  children: comment.user\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this), comment.user_id === user_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-1\",\n                  children: editingComment[comment.id] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-success\",\n                      onClick: () => handleEditComment(post.id, comment.id),\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:check\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 563,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-secondary\",\n                      onClick: () => {\n                        setEditingComment(prev => ({\n                          ...prev,\n                          [comment.id]: false\n                        }));\n                        setEditCommentText(prev => ({\n                          ...prev,\n                          [comment.id]: ''\n                        }));\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:close\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 572,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 565,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-primary\",\n                      onClick: () => {\n                        setEditingComment(prev => ({\n                          ...prev,\n                          [comment.id]: true\n                        }));\n                        setEditCommentText(prev => ({\n                          ...prev,\n                          [comment.id]: comment.text\n                        }));\n                      },\n                      title: \"Edit comment\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:pencil\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 585,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"btn btn-sm btn-outline-danger\",\n                      onClick: () => handleDeleteComment(post.id, comment.id),\n                      title: \"Delete comment\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        icon: \"mdi:delete\",\n                        style: {\n                          fontSize: '0.8rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 592,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 21\n              }, this), editingComment[comment.id] ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"form-control form-control-sm\",\n                  value: editCommentText[comment.id] || '',\n                  onChange: e => handleEditCommentChange(comment.id, e.target.value),\n                  onKeyDown: e => handleEditCommentKeyDown(e, post.id, comment.id),\n                  maxLength: 400,\n                  autoFocus: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-end mt-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: (editCommentText[comment.id] || '').length > 360 ? 'text-warning' : 'text-muted',\n                    children: [(editCommentText[comment.id] || '').length, \"/400 characters\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  wordWrap: 'break-word',\n                  wordBreak: 'break-word',\n                  overflowWrap: 'break-word',\n                  whiteSpace: 'pre-wrap',\n                  maxWidth: '100%'\n                },\n                children: comment.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-muted small mt-1\",\n                children: comment.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 19\n            }, this)]\n          }, comment.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-light rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0 small\",\n              children: \"This is your personal feed where you can see all your posts and updates. Create new posts below and manage your content here.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-end me-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"mb-0\",\n                children: \"My Posts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Your personal posts and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_pic_url) || DefaultProfile,\n              className: \"rounded-circle\",\n              alt: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"Profile\",\n              style: {\n                width: '50px',\n                height: '50px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeedPost, {\n          onPostSubmit: handlePostSubmit,\n          userProfile: userProfile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 11\n        }, this), postingNewPost && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card mb-4\",\n          style: {\n            animation: 'fadeIn 0.5s ease-in-out',\n            border: '2px dashed #007bff',\n            backgroundColor: '#f8f9fa'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-3\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Creating post...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-2\",\n              children: \"Creating your post...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted mb-0\",\n              children: \"Please wait while we process your content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner-border\",\n            role: \"status\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"visually-hidden\",\n              children: \"Loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"Loading your posts...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 13\n        }, this) : posts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:post-outline\",\n            style: {\n              fontSize: '3rem',\n              color: '#6c757d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-muted\",\n            children: \"No posts yet. Be the first to share something!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [posts.map(post => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-body\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: post.user.avatar,\n                  className: \"rounded-circle me-3\",\n                  alt: post.user.name,\n                  style: {\n                    width: '40px',\n                    height: '40px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-grow-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0\",\n                    children: post.user.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: new Date(post.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: [renderPostContent(post.content, post), renderMedia(post.media)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\",\n                  count: post.likes,\n                  onClick: () => handleLike(post.id),\n                  isLiked: post.isLiked,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:comment-outline\",\n                  count: post.commentsCount || 0,\n                  onClick: () => handleComment(post.id),\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n                  icon: \"mdi:share-variant-outline\",\n                  onClick: () => handleShare(post),\n                  isLast: true,\n                  buttonStyle: buttonStyle,\n                  actionButtonStyle: actionButtonStyle\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this), renderComments(post)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 17\n            }, this)\n          }, post.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 15\n          }, this)), loadingMore && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border text-primary mb-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading more posts...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-primary mb-0\",\n              children: \"Loading more posts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 17\n          }, this), !hasMore && posts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-3\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: \"You've reached the end of your posts!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 645,\n    columnNumber: 5\n  }, this);\n};\n_s2(MyFeed, \"qbgoYOYI8eEB6IbomsyyPZE0n3M=\");\n_c3 = MyFeed;\nexport default MyFeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ActionButton$React.memo\");\n$RefreshReg$(_c2, \"ActionButton\");\n$RefreshReg$(_c3, \"MyFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Icon", "DefaultProfile", "FeedPost", "getMyPosts", "toggleLike", "addComment", "getPostComments", "editComment", "deleteComment", "generatePostShareUrl", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ActionButton", "_s", "memo", "_c", "icon", "count", "onClick", "isLiked", "isLast", "buttonStyle", "actionButtonStyle", "buttonClass", "buttonStyleMemo", "marginRight", "className", "style", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "MyFeed", "_s2", "posts", "setPosts", "loading", "setLoading", "loadingMore", "setLoadingMore", "currentPage", "setCurrentPage", "hasMore", "setHasMore", "postingNewPost", "setPostingNewPost", "userProfile", "setUserProfile", "newComment", "setNewComment", "showComments", "setShowComments", "postComments", "setPostComments", "commentsLoading", "setCommentsLoading", "editingComment", "setEditingComment", "editCommentText", "setEditCommentText", "showFullText", "setShowFullText", "user_id", "JSON", "parse", "localStorage", "getItem", "id", "loadMyPosts", "page", "append", "response", "success", "transformedPosts", "data", "map", "post", "user", "name", "user_name", "avatar", "user_avatar", "content", "description", "media", "media_url", "type", "media_type", "url", "is_liked_by_user", "likes", "likes_count", "commentsCount", "comments_count", "created_at", "prev", "length", "profile_pic_url", "pagination", "has_more", "error", "console", "handleScroll", "scrollTop", "document", "documentElement", "scrollHeight", "clientHeight", "loadMorePosts", "window", "addEventListener", "removeEventListener", "backgroundColor", "borderColor", "flex", "loadPostComments", "postId", "newComments", "comments", "comment", "text", "timestamp", "Date", "commented_at", "toLocaleDateString", "year", "month", "day", "handleLike", "handleComment", "isOpening", "handleSubmitComment", "commentText", "trim", "newCommentObj", "handlePostSubmit", "newPost", "log", "setTimeout", "handleShare", "shareUrl", "shareData", "title", "navigator", "share", "clipboard", "writeText", "renderMedia", "width", "maxHeight", "overflow", "borderRadius", "src", "alt", "height", "objectFit", "display", "controls", "renderPostContent", "hasMedia", "shouldTruncate", "isShowingFull", "displayText", "substring", "handleCommentChange", "value", "handleCommentKeyDown", "e", "key", "handleCommentSubmitClick", "handleEditComment", "commentId", "editText", "handleDeleteComment", "confirm", "filter", "handleEditCommentChange", "handleEditCommentKeyDown", "renderComments", "isLoading", "placeholder", "onChange", "target", "onKeyDown", "max<PERSON><PERSON><PERSON>", "disabled", "role", "autoFocus", "wordWrap", "wordBreak", "overflowWrap", "whiteSpace", "max<PERSON><PERSON><PERSON>", "onPostSubmit", "animation", "border", "color", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/feed/MyFeed.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react'\r\nimport { Icon } from '@iconify/react'\r\nimport DefaultProfile from '../../../assets/images/profile/default-profile.png'\r\nimport FeedPost from './FeedPost'\r\nimport { getMyPosts, toggleLike, addComment, getPostComments, editComment, deleteComment, generatePostShareUrl } from '../../../services/feedServices'\r\nimport { toast } from 'react-toastify'\r\n\r\n// Move ActionButton outside to prevent recreation on every render\r\nconst ActionButton = React.memo(({ icon, count, onClick, isLiked, isLast, buttonStyle, actionButtonStyle }) => {\r\n  const buttonClass = useMemo(() =>\r\n    `btn border ${isLiked ? 'text-danger' : 'text-muted'}`,\r\n    [isLiked]\r\n  );\r\n\r\n  const buttonStyleMemo = useMemo(() =>\r\n    isLast ? { ...actionButtonStyle, marginRight: 0 } : actionButtonStyle,\r\n    [isLast, actionButtonStyle]\r\n  );\r\n\r\n  return (\r\n    <button\r\n      className={buttonClass}\r\n      onClick={onClick}\r\n      style={buttonStyleMemo}\r\n    >\r\n      <div className=\"d-flex align-items-center justify-content-center\">\r\n        <Icon icon={icon} style={{fontSize: '1.2rem'}} />\r\n        {count && <span className=\"ms-1\" style={{fontSize: '0.9rem'}}>{count}</span>}\r\n      </div>\r\n    </button>\r\n  );\r\n});\r\n\r\nconst MyFeed = () => {\r\n  // Posts state\r\n  const [posts, setPosts] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [loadingMore, setLoadingMore] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [hasMore, setHasMore] = useState(true);\r\n  const [postingNewPost, setPostingNewPost] = useState(false);\r\n  const [userProfile, setUserProfile] = useState(null);\r\n\r\n  // Comments state\r\n  const [newComment, setNewComment] = useState({});\r\n  const [showComments, setShowComments] = useState({});\r\n  const [postComments, setPostComments] = useState({}); // Store comments for each post\r\n  const [commentsLoading, setCommentsLoading] = useState({}); // Loading state for comments\r\n  const [editingComment, setEditingComment] = useState({}); // Track which comment is being edited\r\n  const [editCommentText, setEditCommentText] = useState({}); // Store edit text for each comment\r\n  const [showFullText, setShowFullText] = useState({}); // Track which posts show full text\r\n\r\n  const user_id = JSON.parse(localStorage.getItem('user')).id;\r\n\r\n  // Load my posts\r\n  const loadMyPosts = useCallback(async (page = 1, append = false) => {\r\n    try {\r\n      if (page === 1) {\r\n        setLoading(true);\r\n      } else {\r\n        setLoadingMore(true);\r\n      }\r\n\r\n      const response = await getMyPosts(page, 5);\r\n\r\n      if (response.success) {\r\n        const transformedPosts = response.data.posts.map(post => ({\r\n          id: post.id,\r\n          user: {\r\n            name: post.user_name,\r\n            avatar: post.user_avatar || DefaultProfile\r\n          },\r\n          content: post.description,\r\n          media: post.media_url ? {\r\n            type: post.media_type,\r\n            url: post.media_url\r\n          } : null,\r\n          isLiked: post.is_liked_by_user === 1,\r\n          likes: post.likes_count,\r\n          commentsCount: post.comments_count,\r\n          created_at: post.created_at\r\n        }));\r\n\r\n        if (append) {\r\n          setPosts(prev => [...prev, ...transformedPosts]);\r\n        } else {\r\n          setPosts(transformedPosts);\r\n          // Store user profile from first post\r\n          if (transformedPosts.length > 0) {\r\n            setUserProfile({\r\n              name: transformedPosts[0].user.name,\r\n              profile_pic_url: transformedPosts[0].user.avatar\r\n            });\r\n          }\r\n        }\r\n\r\n        setCurrentPage(page);\r\n        setHasMore(response.data.pagination.has_more);\r\n      } else {\r\n        toast.error('Failed to load posts');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading posts:', error);\r\n      toast.error('Failed to load posts');\r\n    } finally {\r\n      setLoading(false);\r\n      setLoadingMore(false);\r\n    }\r\n  }, []);\r\n\r\n  // Load initial posts\r\n  useEffect(() => {\r\n    loadMyPosts(1);\r\n  }, [loadMyPosts]);\r\n\r\n  // Infinite scroll handler\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const scrollTop = document.documentElement.scrollTop;\r\n      const scrollHeight = document.documentElement.scrollHeight;\r\n      const clientHeight = document.documentElement.clientHeight;\r\n      \r\n      // Check if user has scrolled to bottom (with 100px threshold)\r\n      if (scrollTop + clientHeight >= scrollHeight - 100) {\r\n        if (!loadingMore && hasMore) {\r\n          loadMorePosts();\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [loadingMore, hasMore]);\r\n\r\n  // Button styles\r\n  // Memoized styles to prevent re-renders\r\n  const buttonStyle = useMemo(() => ({\r\n    backgroundColor: 'transparent',\r\n    borderColor: '#dee2e6'\r\n  }), []);\r\n\r\n  const actionButtonStyle = useMemo(() => ({\r\n    flex: 1,\r\n    marginRight: '10px',\r\n    ...buttonStyle\r\n  }), [buttonStyle]);\r\n\r\n  // Load comments for a specific post\r\n  const loadPostComments = useCallback(async (postId) => {\r\n    try {\r\n      setCommentsLoading(prev => ({ ...prev, [postId]: true }));\r\n\r\n      // Load all comments at once by setting a high page size\r\n      const response = await getPostComments(postId, 1, 1000);\r\n\r\n      if (response.success) {\r\n        const newComments = response.data.comments.map(comment => ({\r\n          id: comment.id,\r\n          user: comment.user_name,\r\n          user_id: comment.user_id,\r\n          avatar: comment.user_avatar || DefaultProfile,\r\n          text: comment.comment,\r\n          timestamp: new Date(comment.commented_at).toLocaleDateString('en-US', {\r\n            year: 'numeric',\r\n            month: 'long',\r\n            day: 'numeric'\r\n          })\r\n        }));\r\n\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: newComments\r\n        }));\r\n      } else {\r\n        toast.error('Failed to load comments');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading comments:', error);\r\n      toast.error('Failed to load comments');\r\n    } finally {\r\n      setCommentsLoading(prev => ({ ...prev, [postId]: false }));\r\n    }\r\n  }, []);\r\n\r\n  // Event handlers\r\n  const handleLike = async (postId) => {\r\n    try {\r\n      const response = await toggleLike(postId);\r\n      if (response.success) {\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? {\r\n                ...post,\r\n                isLiked: !post.isLiked,\r\n                likes: post.isLiked ? post.likes - 1 : post.likes + 1\r\n              }\r\n            : post\r\n        ));\r\n      } else {\r\n        toast.error('Failed to update like');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error toggling like:', error);\r\n      toast.error('Failed to update like');\r\n    }\r\n  };\r\n\r\n  const handleComment = (postId) => {\r\n    const isOpening = !showComments[postId];\r\n    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));\r\n\r\n    // Load comments when opening comments section for the first time\r\n    if (isOpening && !postComments[postId]) {\r\n      loadPostComments(postId);\r\n    }\r\n  };\r\n\r\n  const handleSubmitComment = async (postId) => {\r\n    const commentText = newComment[postId];\r\n    if (!commentText || !commentText.trim()) {\r\n      toast.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (commentText.length > 400) {\r\n      toast.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await addComment(postId, commentText.trim());\r\n      if (response.success) {\r\n        // Update the post's comments count\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? { ...post, commentsCount: post.commentsCount + 1 }\r\n            : post\r\n        ));\r\n\r\n        // Add the new comment to the comments list\r\n        const newCommentObj = {\r\n          id: response.data.comment.id,\r\n          user: response.data.comment.user_name,\r\n          user_id: response.data.comment.user_id,\r\n          avatar: response.data.comment.user_avatar || DefaultProfile,\r\n          text: response.data.comment.comment,\r\n          timestamp: 'Just now'\r\n        };\r\n\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: [newCommentObj, ...(prev[postId] || [])]\r\n        }));\r\n\r\n        setNewComment(prev => ({ ...prev, [postId]: '' }));\r\n        toast.success('Comment added successfully');\r\n      } else {\r\n        toast.error('Failed to add comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n      toast.error('Failed to add comment');\r\n    }\r\n  };\r\n\r\n  const handlePostSubmit = async (newPost) => {\r\n    console.log('handlePostSubmit called with:', newPost);\r\n    setPostingNewPost(true);\r\n\r\n    // Simulate API delay and then refresh the feed\r\n    setTimeout(async () => {\r\n      try {\r\n        await loadMyPosts(1); // Reload posts to include the new one\r\n        toast.success('Post created successfully!');\r\n      } finally {\r\n        setPostingNewPost(false);\r\n      }\r\n    }, 2000); // 2 second delay\r\n  };\r\n\r\n  const loadMorePosts = useCallback(() => {\r\n    if (!loadingMore && hasMore) {\r\n      console.log('Loading more posts...', { currentPage: currentPage + 1, hasMore });\r\n      loadMyPosts(currentPage + 1, true);\r\n    }\r\n  }, [loadMyPosts, loadingMore, hasMore, currentPage]);\r\n\r\n  const handleShare = async (post) => {\r\n    try {\r\n      // Generate shareable URL for the post\r\n      const response = await generatePostShareUrl(post.id);\r\n\r\n      if (response.success) {\r\n        const shareUrl = response.data.shareUrl;\r\n\r\n        // Prepare share data\r\n        const shareData = {\r\n          title: `${post.user.name}'s Post`,\r\n          text: post.content || 'Check out this post!',\r\n          url: shareUrl\r\n        };\r\n\r\n        // Check if Web Share API is supported\r\n        if (navigator.share) {\r\n          await navigator.share(shareData);\r\n          console.log('Shared successfully');\r\n        } else {\r\n          // Fallback for browsers that don't support Web Share API\r\n          // Copy to clipboard\r\n          await navigator.clipboard.writeText(shareUrl);\r\n          toast.success('Post link copied to clipboard!');\r\n        }\r\n      } else {\r\n        toast.error('Failed to generate share link');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error sharing post:', error);\r\n      if (error.name !== 'AbortError') {\r\n        toast.error('Failed to share post');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Render functions\r\n  const renderMedia = (media) => {\r\n    if (!media) return null;\r\n\r\n    if (media.type === 'image') {\r\n      return (\r\n        <div className=\"position-relative\" style={{ width: '100%', maxHeight: '400px', overflow: 'hidden', borderRadius: '8px' }}>\r\n          <img \r\n            src={media.url} \r\n            className=\"img-fluid\" \r\n            alt=\"Post media\" \r\n            style={{\r\n              width: '100%',\r\n              height: 'auto',\r\n              maxHeight: '400px',\r\n              objectFit: 'contain',\r\n              display: 'block'\r\n            }} \r\n          />\r\n        </div>\r\n      );\r\n    } else if (media.type === 'video') {\r\n      return (\r\n        <div className=\"position-relative\" style={{ width: '100%', maxHeight: '400px', overflow: 'hidden', borderRadius: '8px' }}>\r\n          <video \r\n            className=\"img-fluid\" \r\n            controls \r\n            style={{\r\n              width: '100%',\r\n              height: 'auto',\r\n              maxHeight: '400px',\r\n              objectFit: 'contain',\r\n              display: 'block'\r\n            }}\r\n          >\r\n            <source src={media.url} type=\"video/mp4\" />\r\n            Your browser does not support the video tag.\r\n          </video>\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const renderPostContent = (content, post) => {\r\n    if (!content) return null;\r\n\r\n    const hasMedia = post.media && (post.media.type === 'image' || post.media.type === 'video');\r\n\r\n    // For text-only posts, show full content\r\n    if (!hasMedia) {\r\n      return (\r\n        <div>\r\n          <p className=\"card-text mb-2\">{content}</p>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // For posts with media, show truncated text with \"Show more\" option\r\n    const shouldTruncate = content.length > 100;\r\n    const isShowingFull = showFullText[post.id];\r\n    const displayText = isShowingFull ? content : content.substring(0, 100) + (shouldTruncate ? '...' : '');\r\n\r\n    return (\r\n      <div>\r\n        <p className=\"card-text mb-2\">\r\n          {displayText}\r\n          {shouldTruncate && (\r\n            <button\r\n              className=\"btn btn-link p-0 ms-2 text-primary text-decoration-none\"\r\n              onClick={() => setShowFullText(prev => ({ ...prev, [post.id]: !isShowingFull }))}\r\n            >\r\n              {isShowingFull ? 'Show less' : 'Show more'}\r\n            </button>\r\n          )}\r\n        </p>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Memoized comment handlers to prevent re-renders\r\n  const handleCommentChange = useCallback((postId, value) => {\r\n    setNewComment(prev => ({ ...prev, [postId]: value }));\r\n  }, []);\r\n\r\n  const handleCommentKeyDown = useCallback((e, postId) => {\r\n    if (e.key === 'Enter') {\r\n      handleSubmitComment(postId);\r\n    }\r\n  }, [handleSubmitComment]);\r\n\r\n  const handleCommentSubmitClick = useCallback((postId) => {\r\n    handleSubmitComment(postId);\r\n  }, [handleSubmitComment]);\r\n\r\n  const handleEditComment = async (postId, commentId) => {\r\n    const editText = editCommentText[commentId];\r\n    if (!editText || !editText.trim()) {\r\n      toast.error('Please enter a comment');\r\n      return;\r\n    }\r\n\r\n    if (editText.length > 400) {\r\n      toast.error('Comment cannot exceed 400 characters');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await editComment(commentId, editText.trim());\r\n      if (response.success) {\r\n        // Update the comment in the comments list\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: prev[postId].map(comment =>\r\n            comment.id === commentId\r\n              ? { ...comment, text: editText.trim() }\r\n              : comment\r\n          )\r\n        }));\r\n\r\n        setEditingComment(prev => ({ ...prev, [commentId]: false }));\r\n        setEditCommentText(prev => ({ ...prev, [commentId]: '' }));\r\n        toast.success('Comment updated successfully');\r\n      } else {\r\n        toast.error('Failed to update comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n      toast.error('Failed to update comment');\r\n    }\r\n  };\r\n\r\n  const handleDeleteComment = async (postId, commentId) => {\r\n    if (!window.confirm('Are you sure you want to delete this comment?')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await deleteComment(commentId);\r\n      if (response.success) {\r\n        // Update the post's comments count\r\n        setPosts(posts.map(post =>\r\n          post.id === postId\r\n            ? { ...post, commentsCount: post.commentsCount - 1 }\r\n            : post\r\n        ));\r\n\r\n        // Remove the comment from the comments list\r\n        setPostComments(prev => ({\r\n          ...prev,\r\n          [postId]: prev[postId].filter(comment => comment.id !== commentId)\r\n        }));\r\n\r\n        toast.success('Comment deleted successfully');\r\n      } else {\r\n        toast.error('Failed to delete comment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting comment:', error);\r\n      toast.error('Failed to delete comment');\r\n    }\r\n  };\r\n\r\n  const handleEditCommentChange = useCallback((commentId, value) => {\r\n    setEditCommentText(prev => ({ ...prev, [commentId]: value }));\r\n  }, []);\r\n\r\n  const handleEditCommentKeyDown = useCallback((e, postId, commentId) => {\r\n    if (e.key === 'Enter') {\r\n      handleEditComment(postId, commentId);\r\n    }\r\n  }, [handleEditComment]);\r\n\r\n  const renderComments = (post) => {\r\n    if (!showComments[post.id]) return null;\r\n\r\n    const comments = postComments[post.id] || [];\r\n    const isLoading = commentsLoading[post.id];\r\n\r\n    return (\r\n      <div className=\"border-top pt-3 mt-3\">\r\n        <h6 className=\"mb-3\">Comments ({post.commentsCount || 0})</h6>\r\n\r\n        {/* Comment Input */}\r\n        <div className=\"d-flex mb-3\">\r\n          <img src={userProfile?.profile_pic_url || DefaultProfile} className=\"rounded-circle me-2\" alt=\"Profile\" style={{width: '32px', height: '32px'}} />\r\n          <div className=\"flex-grow-1\">\r\n            <input\r\n              type=\"text\"\r\n              className=\"form-control\"\r\n              placeholder=\"Write a comment...\"\r\n              value={newComment[post.id] || ''}\r\n              onChange={(e) => handleCommentChange(post.id, e.target.value)}\r\n              onKeyDown={(e) => handleCommentKeyDown(e, post.id)}\r\n              maxLength={400}\r\n            />\r\n            <div className=\"d-flex justify-content-end mt-1\">\r\n              <small className={(newComment[post.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\r\n                {(newComment[post.id] || '').length}/400 characters\r\n              </small>\r\n            </div>\r\n          </div>\r\n          <button\r\n            className=\"btn btn-primary btn-sm ms-2 w-auto\"\r\n            onClick={() => handleCommentSubmitClick(post.id)}\r\n            disabled={!newComment[post.id] || !newComment[post.id].trim()}\r\n          >\r\n            <Icon icon=\"mdi:send\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Comments Loading State */}\r\n        {isLoading ? (\r\n          <div className=\"text-center py-3\">\r\n            <div className=\"spinner-border spinner-border-sm\" role=\"status\">\r\n              <span className=\"visually-hidden\">Loading comments...</span>\r\n            </div>\r\n            <p className=\"mt-2 text-muted small\">Loading comments...</p>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Comments Container */}\r\n            <div id={`comments-container-${post.id}`}>\r\n              {/* Existing Comments */}\r\n              {comments.map(comment => (\r\n                <div key={comment.id} className=\"d-flex mb-2\">\r\n                  <img src={comment.avatar} className=\"rounded-circle me-2\" alt={comment.user} style={{width: '32px', height: '32px'}} />\r\n                  <div className=\"bg-light rounded p-2 flex-grow-1\">\r\n                    <div className=\"d-flex justify-content-between align-items-start\">\r\n                      <div className=\"fw-bold\">{comment.user}</div>\r\n                      {/* Show edit/delete options only for user's own comments */}\r\n                      {comment.user_id === user_id && (\r\n                        <div className=\"d-flex gap-1\">\r\n                          {editingComment[comment.id] ? (\r\n                            <>\r\n                              <button\r\n                                className=\"btn btn-sm btn-success\"\r\n                                onClick={() => handleEditComment(post.id, comment.id)}\r\n                              >\r\n                                <Icon icon=\"mdi:check\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                              <button\r\n                                className=\"btn btn-sm btn-secondary\"\r\n                                onClick={() => {\r\n                                  setEditingComment(prev => ({ ...prev, [comment.id]: false }));\r\n                                  setEditCommentText(prev => ({ ...prev, [comment.id]: '' }));\r\n                                }}\r\n                              >\r\n                                <Icon icon=\"mdi:close\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <button\r\n                                className=\"btn btn-sm btn-outline-primary\"\r\n                                onClick={() => {\r\n                                  setEditingComment(prev => ({ ...prev, [comment.id]: true }));\r\n                                  setEditCommentText(prev => ({ ...prev, [comment.id]: comment.text }));\r\n                                }}\r\n                                title=\"Edit comment\"\r\n                              >\r\n                                <Icon icon=\"mdi:pencil\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                              <button\r\n                                className=\"btn btn-sm btn-outline-danger\"\r\n                                onClick={() => handleDeleteComment(post.id, comment.id)}\r\n                                title=\"Delete comment\"\r\n                              >\r\n                                <Icon icon=\"mdi:delete\" style={{fontSize: '0.8rem'}} />\r\n                              </button>\r\n                            </>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    \r\n                    {editingComment[comment.id] ? (\r\n                      <div className=\"mt-2\">\r\n                        <input\r\n                          type=\"text\"\r\n                          className=\"form-control form-control-sm\"\r\n                          value={editCommentText[comment.id] || ''}\r\n                          onChange={(e) => handleEditCommentChange(comment.id, e.target.value)}\r\n                          onKeyDown={(e) => handleEditCommentKeyDown(e, post.id, comment.id)}\r\n                          maxLength={400}\r\n                          autoFocus\r\n                        />\r\n                        <div className=\"d-flex justify-content-end mt-1\">\r\n                          <small className={(editCommentText[comment.id] || '').length > 360 ? 'text-warning' : 'text-muted'}>\r\n                            {(editCommentText[comment.id] || '').length}/400 characters\r\n                          </small>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <div style={{\r\n                        wordWrap: 'break-word',\r\n                        wordBreak: 'break-word',\r\n                        overflowWrap: 'break-word',\r\n                        whiteSpace: 'pre-wrap',\r\n                        maxWidth: '100%'\r\n                      }}>\r\n                        {comment.text}\r\n                      </div>\r\n                    )}\r\n                    \r\n                    <div className=\"text-muted small mt-1\">{comment.timestamp}</div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n\r\n\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"container py-4\">\r\n      <div className=\"row justify-content-center\">\r\n        <div className=\"col-md-8\">\r\n                    {/* Feed Info */}\r\n                    <div className=\"text-center mb-4\">\r\n            <div className=\"p-3 bg-light rounded\">\r\n              <p className=\"text-muted mb-0 small\">\r\n                This is your personal feed where you can see all your posts and updates. \r\n                Create new posts below and manage your content here.\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Profile Header */}\r\n          <div className=\"d-flex justify-content-between align-items-center mb-4\">\r\n            <div></div>\r\n            <div className=\"d-flex align-items-center\">\r\n              <div className=\"text-end me-3\">\r\n                <h5 className=\"mb-0\">My Posts</h5>\r\n                <small className=\"text-muted\">Your personal posts and updates</small>\r\n              </div>\r\n              <img \r\n                src={userProfile?.profile_pic_url || DefaultProfile} \r\n                className=\"rounded-circle\" \r\n                alt={userProfile?.name || \"Profile\"} \r\n                style={{width: '50px', height: '50px'}} \r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Create Post Component */}\r\n          <FeedPost onPostSubmit={handlePostSubmit} userProfile={userProfile} />\r\n\r\n\r\n          {/* New Post Loading State */}\r\n          {postingNewPost && (\r\n            <div className=\"card mb-4\" style={{\r\n              animation: 'fadeIn 0.5s ease-in-out',\r\n              border: '2px dashed #007bff',\r\n              backgroundColor: '#f8f9fa'\r\n            }}>\r\n              <div className=\"card-body text-center py-4\">\r\n                <div className=\"spinner-border text-primary mb-3\" role=\"status\">\r\n                  <span className=\"visually-hidden\">Creating post...</span>\r\n                </div>\r\n                <h6 className=\"text-primary mb-2\">Creating your post...</h6>\r\n                <p className=\"text-muted mb-0\">Please wait while we process your content</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Loading State */}\r\n          {loading ? (\r\n            <div className=\"text-center py-4\">\r\n              <div className=\"spinner-border\" role=\"status\">\r\n                <span className=\"visually-hidden\">Loading...</span>\r\n              </div>\r\n              <p className=\"mt-2 text-muted\">Loading your posts...</p>\r\n            </div>\r\n          ) : posts.length === 0 ? (\r\n            <div className=\"text-center py-4\">\r\n              <Icon icon=\"mdi:post-outline\" style={{ fontSize: '3rem', color: '#6c757d' }} />\r\n              <p className=\"mt-2 text-muted\">No posts yet. Be the first to share something!</p>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Posts Feed */}\r\n              {posts.map((post) => (\r\n              <div key={post.id} className=\"card mb-4\">\r\n                <div className=\"card-body\">\r\n                  {/* Post Header */}\r\n                  <div className=\"d-flex align-items-center mb-3\">\r\n                    <img src={post.user.avatar} className=\"rounded-circle me-3\" alt={post.user.name} style={{width: '40px', height: '40px'}} />\r\n                    <div className=\"flex-grow-1\">\r\n                      <h6 className=\"mb-0\">{post.user.name}</h6>\r\n                      <small className=\"text-muted\">{new Date(post.created_at).toLocaleDateString()}</small>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Post Content */}\r\n                  <div className=\"mb-3\">\r\n                    {renderPostContent(post.content, post)}\r\n                    {renderMedia(post.media)}\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"d-flex justify-content-between\">\r\n                    <ActionButton\r\n                      icon={post.isLiked ? \"mdi:heart\" : \"mdi:heart-outline\"}\r\n                      count={post.likes}\r\n                      onClick={() => handleLike(post.id)}\r\n                      isLiked={post.isLiked}\r\n                      buttonStyle={buttonStyle}\r\n                      actionButtonStyle={actionButtonStyle}\r\n                    />\r\n                    <ActionButton\r\n                      icon=\"mdi:comment-outline\"\r\n                      count={post.commentsCount || 0}\r\n                      onClick={() => handleComment(post.id)}\r\n                      buttonStyle={buttonStyle}\r\n                      actionButtonStyle={actionButtonStyle}\r\n                    />\r\n                    <ActionButton\r\n                      icon=\"mdi:share-variant-outline\"\r\n                      onClick={() => handleShare(post)}\r\n                      isLast={true}\r\n                      buttonStyle={buttonStyle}\r\n                      actionButtonStyle={actionButtonStyle}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Comments Section */}\r\n                  {renderComments(post)}\r\n                </div>\r\n              </div>\r\n              ))}\r\n\r\n              {/* Loading More Posts Indicator */}\r\n              {loadingMore && (\r\n                <div className=\"text-center py-4\">\r\n                  <div className=\"spinner-border text-primary mb-2\" role=\"status\">\r\n                    <span className=\"visually-hidden\">Loading more posts...</span>\r\n                  </div>\r\n                  <p className=\"text-primary mb-0\">Loading more posts...</p>\r\n                </div>\r\n              )}\r\n\r\n              {!hasMore && posts.length > 0 && (\r\n                <div className=\"text-center py-3\">\r\n                  <p className=\"text-muted\">You've reached the end of your posts!</p>\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MyFeed; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,cAAc,MAAM,oDAAoD;AAC/E,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,UAAU,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,WAAW,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,gCAAgC;AACtJ,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,gBAAAC,EAAA,cAAGrB,KAAK,CAACsB,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,IAAI;EAAEC,KAAK;EAAEC,OAAO;EAAEC,OAAO;EAAEC,MAAM;EAAEC,WAAW;EAAEC;AAAkB,CAAC,KAAK;EAAAT,EAAA;EAC7G,MAAMU,WAAW,GAAG3B,OAAO,CAAC,MAC1B,cAAcuB,OAAO,GAAG,aAAa,GAAG,YAAY,EAAE,EACtD,CAACA,OAAO,CACV,CAAC;EAED,MAAMK,eAAe,GAAG5B,OAAO,CAAC,MAC9BwB,MAAM,GAAG;IAAE,GAAGE,iBAAiB;IAAEG,WAAW,EAAE;EAAE,CAAC,GAAGH,iBAAiB,EACrE,CAACF,MAAM,EAAEE,iBAAiB,CAC5B,CAAC;EAED,oBACEb,OAAA;IACEiB,SAAS,EAAEH,WAAY;IACvBL,OAAO,EAAEA,OAAQ;IACjBS,KAAK,EAAEH,eAAgB;IAAAI,QAAA,eAEvBnB,OAAA;MAAKiB,SAAS,EAAC,kDAAkD;MAAAE,QAAA,gBAC/DnB,OAAA,CAACZ,IAAI;QAACmB,IAAI,EAAEA,IAAK;QAACW,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAChDhB,KAAK,iBAAIR,OAAA;QAAMiB,SAAS,EAAC,MAAM;QAACC,KAAK,EAAE;UAACE,QAAQ,EAAE;QAAQ,CAAE;QAAAD,QAAA,EAAEX;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC,kCAAC;AAACC,GAAA,GAvBGtB,YAAY;AAyBlB,MAAMuB,MAAM,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnB;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEtD,MAAMwE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE;;EAE3D;EACA,MAAMC,WAAW,GAAG5E,WAAW,CAAC,OAAO6E,IAAI,GAAG,CAAC,EAAEC,MAAM,GAAG,KAAK,KAAK;IAClE,IAAI;MACF,IAAID,IAAI,KAAK,CAAC,EAAE;QACdhC,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLE,cAAc,CAAC,IAAI,CAAC;MACtB;MAEA,MAAMgC,QAAQ,GAAG,MAAM1E,UAAU,CAACwE,IAAI,EAAE,CAAC,CAAC;MAE1C,IAAIE,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMC,gBAAgB,GAAGF,QAAQ,CAACG,IAAI,CAACxC,KAAK,CAACyC,GAAG,CAACC,IAAI,KAAK;UACxDT,EAAE,EAAES,IAAI,CAACT,EAAE;UACXU,IAAI,EAAE;YACJC,IAAI,EAAEF,IAAI,CAACG,SAAS;YACpBC,MAAM,EAAEJ,IAAI,CAACK,WAAW,IAAItF;UAC9B,CAAC;UACDuF,OAAO,EAAEN,IAAI,CAACO,WAAW;UACzBC,KAAK,EAAER,IAAI,CAACS,SAAS,GAAG;YACtBC,IAAI,EAAEV,IAAI,CAACW,UAAU;YACrBC,GAAG,EAAEZ,IAAI,CAACS;UACZ,CAAC,GAAG,IAAI;UACRrE,OAAO,EAAE4D,IAAI,CAACa,gBAAgB,KAAK,CAAC;UACpCC,KAAK,EAAEd,IAAI,CAACe,WAAW;UACvBC,aAAa,EAAEhB,IAAI,CAACiB,cAAc;UAClCC,UAAU,EAAElB,IAAI,CAACkB;QACnB,CAAC,CAAC,CAAC;QAEH,IAAIxB,MAAM,EAAE;UACVnC,QAAQ,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGtB,gBAAgB,CAAC,CAAC;QAClD,CAAC,MAAM;UACLtC,QAAQ,CAACsC,gBAAgB,CAAC;UAC1B;UACA,IAAIA,gBAAgB,CAACuB,MAAM,GAAG,CAAC,EAAE;YAC/BjD,cAAc,CAAC;cACb+B,IAAI,EAAEL,gBAAgB,CAAC,CAAC,CAAC,CAACI,IAAI,CAACC,IAAI;cACnCmB,eAAe,EAAExB,gBAAgB,CAAC,CAAC,CAAC,CAACI,IAAI,CAACG;YAC5C,CAAC,CAAC;UACJ;QACF;QAEAvC,cAAc,CAAC4B,IAAI,CAAC;QACpB1B,UAAU,CAAC4B,QAAQ,CAACG,IAAI,CAACwB,UAAU,CAACC,QAAQ,CAAC;MAC/C,CAAC,MAAM;QACL/F,KAAK,CAACgG,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChG,KAAK,CAACgG,KAAK,CAAC,sBAAsB,CAAC;IACrC,CAAC,SAAS;MACR/D,UAAU,CAAC,KAAK,CAAC;MACjBE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhD,SAAS,CAAC,MAAM;IACd6E,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA7E,SAAS,CAAC,MAAM;IACd,MAAM+G,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,SAAS,GAAGC,QAAQ,CAACC,eAAe,CAACF,SAAS;MACpD,MAAMG,YAAY,GAAGF,QAAQ,CAACC,eAAe,CAACC,YAAY;MAC1D,MAAMC,YAAY,GAAGH,QAAQ,CAACC,eAAe,CAACE,YAAY;;MAE1D;MACA,IAAIJ,SAAS,GAAGI,YAAY,IAAID,YAAY,GAAG,GAAG,EAAE;QAClD,IAAI,CAACpE,WAAW,IAAII,OAAO,EAAE;UAC3BkE,aAAa,CAAC,CAAC;QACjB;MACF;IACF,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAER,YAAY,CAAC;IAC/C,OAAO,MAAMO,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAET,YAAY,CAAC;EACjE,CAAC,EAAE,CAAChE,WAAW,EAAEI,OAAO,CAAC,CAAC;;EAE1B;EACA;EACA,MAAMxB,WAAW,GAAGzB,OAAO,CAAC,OAAO;IACjCuH,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC,EAAE,EAAE,CAAC;EAEP,MAAM9F,iBAAiB,GAAG1B,OAAO,CAAC,OAAO;IACvCyH,IAAI,EAAE,CAAC;IACP5F,WAAW,EAAE,MAAM;IACnB,GAAGJ;EACL,CAAC,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAElB;EACA,MAAMiG,gBAAgB,GAAG3H,WAAW,CAAC,MAAO4H,MAAM,IAAK;IACrD,IAAI;MACF7D,kBAAkB,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACqB,MAAM,GAAG;MAAK,CAAC,CAAC,CAAC;;MAEzD;MACA,MAAM7C,QAAQ,GAAG,MAAMvE,eAAe,CAACoH,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC;MAEvD,IAAI7C,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAM6C,WAAW,GAAG9C,QAAQ,CAACG,IAAI,CAAC4C,QAAQ,CAAC3C,GAAG,CAAC4C,OAAO,KAAK;UACzDpD,EAAE,EAAEoD,OAAO,CAACpD,EAAE;UACdU,IAAI,EAAE0C,OAAO,CAACxC,SAAS;UACvBjB,OAAO,EAAEyD,OAAO,CAACzD,OAAO;UACxBkB,MAAM,EAAEuC,OAAO,CAACtC,WAAW,IAAItF,cAAc;UAC7C6H,IAAI,EAAED,OAAO,CAACA,OAAO;UACrBE,SAAS,EAAE,IAAIC,IAAI,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;YACpEC,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE,MAAM;YACbC,GAAG,EAAE;UACP,CAAC;QACH,CAAC,CAAC,CAAC;QAEH1E,eAAe,CAAC0C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAGC;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLjH,KAAK,CAACgG,KAAK,CAAC,yBAAyB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChG,KAAK,CAACgG,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACR7C,kBAAkB,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACqB,MAAM,GAAG;MAAM,CAAC,CAAC,CAAC;IAC5D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,UAAU,GAAG,MAAOZ,MAAM,IAAK;IACnC,IAAI;MACF,MAAM7C,QAAQ,GAAG,MAAMzE,UAAU,CAACsH,MAAM,CAAC;MACzC,IAAI7C,QAAQ,CAACC,OAAO,EAAE;QACpBrC,QAAQ,CAACD,KAAK,CAACyC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACT,EAAE,KAAKiD,MAAM,GACd;UACE,GAAGxC,IAAI;UACP5D,OAAO,EAAE,CAAC4D,IAAI,CAAC5D,OAAO;UACtB0E,KAAK,EAAEd,IAAI,CAAC5D,OAAO,GAAG4D,IAAI,CAACc,KAAK,GAAG,CAAC,GAAGd,IAAI,CAACc,KAAK,GAAG;QACtD,CAAC,GACDd,IACN,CAAC,CAAC;MACJ,CAAC,MAAM;QACLxE,KAAK,CAACgG,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChG,KAAK,CAACgG,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAM6B,aAAa,GAAIb,MAAM,IAAK;IAChC,MAAMc,SAAS,GAAG,CAAChF,YAAY,CAACkE,MAAM,CAAC;IACvCjE,eAAe,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACqB,MAAM,GAAG,CAACrB,IAAI,CAACqB,MAAM;IAAE,CAAC,CAAC,CAAC;;IAE/D;IACA,IAAIc,SAAS,IAAI,CAAC9E,YAAY,CAACgE,MAAM,CAAC,EAAE;MACtCD,gBAAgB,CAACC,MAAM,CAAC;IAC1B;EACF,CAAC;EAED,MAAMe,mBAAmB,GAAG,MAAOf,MAAM,IAAK;IAC5C,MAAMgB,WAAW,GAAGpF,UAAU,CAACoE,MAAM,CAAC;IACtC,IAAI,CAACgB,WAAW,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;MACvCjI,KAAK,CAACgG,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAIgC,WAAW,CAACpC,MAAM,GAAG,GAAG,EAAE;MAC5B5F,KAAK,CAACgG,KAAK,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMxE,UAAU,CAACqH,MAAM,EAAEgB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;MAC7D,IAAI9D,QAAQ,CAACC,OAAO,EAAE;QACpB;QACArC,QAAQ,CAACD,KAAK,CAACyC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACT,EAAE,KAAKiD,MAAM,GACd;UAAE,GAAGxC,IAAI;UAAEgB,aAAa,EAAEhB,IAAI,CAACgB,aAAa,GAAG;QAAE,CAAC,GAClDhB,IACN,CAAC,CAAC;;QAEF;QACA,MAAM0D,aAAa,GAAG;UACpBnE,EAAE,EAAEI,QAAQ,CAACG,IAAI,CAAC6C,OAAO,CAACpD,EAAE;UAC5BU,IAAI,EAAEN,QAAQ,CAACG,IAAI,CAAC6C,OAAO,CAACxC,SAAS;UACrCjB,OAAO,EAAES,QAAQ,CAACG,IAAI,CAAC6C,OAAO,CAACzD,OAAO;UACtCkB,MAAM,EAAET,QAAQ,CAACG,IAAI,CAAC6C,OAAO,CAACtC,WAAW,IAAItF,cAAc;UAC3D6H,IAAI,EAAEjD,QAAQ,CAACG,IAAI,CAAC6C,OAAO,CAACA,OAAO;UACnCE,SAAS,EAAE;QACb,CAAC;QAEDpE,eAAe,CAAC0C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAG,CAACkB,aAAa,EAAE,IAAIvC,IAAI,CAACqB,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEHnE,aAAa,CAAC8C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACqB,MAAM,GAAG;QAAG,CAAC,CAAC,CAAC;QAClDhH,KAAK,CAACoE,OAAO,CAAC,4BAA4B,CAAC;MAC7C,CAAC,MAAM;QACLpE,KAAK,CAACgG,KAAK,CAAC,uBAAuB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7ChG,KAAK,CAACgG,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMmC,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1CnC,OAAO,CAACoC,GAAG,CAAC,+BAA+B,EAAED,OAAO,CAAC;IACrD3F,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACA6F,UAAU,CAAC,YAAY;MACrB,IAAI;QACF,MAAMtE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACtBhE,KAAK,CAACoE,OAAO,CAAC,4BAA4B,CAAC;MAC7C,CAAC,SAAS;QACR3B,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAM+D,aAAa,GAAGpH,WAAW,CAAC,MAAM;IACtC,IAAI,CAAC8C,WAAW,IAAII,OAAO,EAAE;MAC3B2D,OAAO,CAACoC,GAAG,CAAC,uBAAuB,EAAE;QAAEjG,WAAW,EAAEA,WAAW,GAAG,CAAC;QAAEE;MAAQ,CAAC,CAAC;MAC/E0B,WAAW,CAAC5B,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;IACpC;EACF,CAAC,EAAE,CAAC4B,WAAW,EAAE9B,WAAW,EAAEI,OAAO,EAAEF,WAAW,CAAC,CAAC;EAEpD,MAAMmG,WAAW,GAAG,MAAO/D,IAAI,IAAK;IAClC,IAAI;MACF;MACA,MAAML,QAAQ,GAAG,MAAMpE,oBAAoB,CAACyE,IAAI,CAACT,EAAE,CAAC;MAEpD,IAAII,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMoE,QAAQ,GAAGrE,QAAQ,CAACG,IAAI,CAACkE,QAAQ;;QAEvC;QACA,MAAMC,SAAS,GAAG;UAChBC,KAAK,EAAE,GAAGlE,IAAI,CAACC,IAAI,CAACC,IAAI,SAAS;UACjC0C,IAAI,EAAE5C,IAAI,CAACM,OAAO,IAAI,sBAAsB;UAC5CM,GAAG,EAAEoD;QACP,CAAC;;QAED;QACA,IAAIG,SAAS,CAACC,KAAK,EAAE;UACnB,MAAMD,SAAS,CAACC,KAAK,CAACH,SAAS,CAAC;UAChCxC,OAAO,CAACoC,GAAG,CAAC,qBAAqB,CAAC;QACpC,CAAC,MAAM;UACL;UACA;UACA,MAAMM,SAAS,CAACE,SAAS,CAACC,SAAS,CAACN,QAAQ,CAAC;UAC7CxI,KAAK,CAACoE,OAAO,CAAC,gCAAgC,CAAC;QACjD;MACF,CAAC,MAAM;QACLpE,KAAK,CAACgG,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAIA,KAAK,CAACtB,IAAI,KAAK,YAAY,EAAE;QAC/B1E,KAAK,CAACgG,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACF;EACF,CAAC;;EAED;EACA,MAAM+C,WAAW,GAAI/D,KAAK,IAAK;IAC7B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAIA,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MAC1B,oBACEhF,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAACC,KAAK,EAAE;UAAE4H,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE,OAAO;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAA9H,QAAA,eACvHnB,OAAA;UACEkJ,GAAG,EAAEpE,KAAK,CAACI,GAAI;UACfjE,SAAS,EAAC,WAAW;UACrBkI,GAAG,EAAC,YAAY;UAChBjI,KAAK,EAAE;YACL4H,KAAK,EAAE,MAAM;YACbM,MAAM,EAAE,MAAM;YACdL,SAAS,EAAE,OAAO;YAClBM,SAAS,EAAE,SAAS;YACpBC,OAAO,EAAE;UACX;QAAE;UAAAjI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV,CAAC,MAAM,IAAIsD,KAAK,CAACE,IAAI,KAAK,OAAO,EAAE;MACjC,oBACEhF,OAAA;QAAKiB,SAAS,EAAC,mBAAmB;QAACC,KAAK,EAAE;UAAE4H,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE,OAAO;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAM,CAAE;QAAA9H,QAAA,eACvHnB,OAAA;UACEiB,SAAS,EAAC,WAAW;UACrBsI,QAAQ;UACRrI,KAAK,EAAE;YACL4H,KAAK,EAAE,MAAM;YACbM,MAAM,EAAE,MAAM;YACdL,SAAS,EAAE,OAAO;YAClBM,SAAS,EAAE,SAAS;YACpBC,OAAO,EAAE;UACX,CAAE;UAAAnI,QAAA,gBAEFnB,OAAA;YAAQkJ,GAAG,EAAEpE,KAAK,CAACI,GAAI;YAACF,IAAI,EAAC;UAAW;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gDAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMgI,iBAAiB,GAAGA,CAAC5E,OAAO,EAAEN,IAAI,KAAK;IAC3C,IAAI,CAACM,OAAO,EAAE,OAAO,IAAI;IAEzB,MAAM6E,QAAQ,GAAGnF,IAAI,CAACQ,KAAK,KAAKR,IAAI,CAACQ,KAAK,CAACE,IAAI,KAAK,OAAO,IAAIV,IAAI,CAACQ,KAAK,CAACE,IAAI,KAAK,OAAO,CAAC;;IAE3F;IACA,IAAI,CAACyE,QAAQ,EAAE;MACb,oBACEzJ,OAAA;QAAAmB,QAAA,eACEnB,OAAA;UAAGiB,SAAS,EAAC,gBAAgB;UAAAE,QAAA,EAAEyD;QAAO;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAEV;;IAEA;IACA,MAAMkI,cAAc,GAAG9E,OAAO,CAACc,MAAM,GAAG,GAAG;IAC3C,MAAMiE,aAAa,GAAGrG,YAAY,CAACgB,IAAI,CAACT,EAAE,CAAC;IAC3C,MAAM+F,WAAW,GAAGD,aAAa,GAAG/E,OAAO,GAAGA,OAAO,CAACiF,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,cAAc,GAAG,KAAK,GAAG,EAAE,CAAC;IAEvG,oBACE1J,OAAA;MAAAmB,QAAA,eACEnB,OAAA;QAAGiB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,GAC1ByI,WAAW,EACXF,cAAc,iBACb1J,OAAA;UACEiB,SAAS,EAAC,yDAAyD;UACnER,OAAO,EAAEA,CAAA,KAAM8C,eAAe,CAACkC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACnB,IAAI,CAACT,EAAE,GAAG,CAAC8F;UAAc,CAAC,CAAC,CAAE;UAAAxI,QAAA,EAEhFwI,aAAa,GAAG,WAAW,GAAG;QAAW;UAAAtI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV,CAAC;;EAED;EACA,MAAMsI,mBAAmB,GAAG5K,WAAW,CAAC,CAAC4H,MAAM,EAAEiD,KAAK,KAAK;IACzDpH,aAAa,CAAC8C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACqB,MAAM,GAAGiD;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,oBAAoB,GAAG9K,WAAW,CAAC,CAAC+K,CAAC,EAAEnD,MAAM,KAAK;IACtD,IAAImD,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBrC,mBAAmB,CAACf,MAAM,CAAC;IAC7B;EACF,CAAC,EAAE,CAACe,mBAAmB,CAAC,CAAC;EAEzB,MAAMsC,wBAAwB,GAAGjL,WAAW,CAAE4H,MAAM,IAAK;IACvDe,mBAAmB,CAACf,MAAM,CAAC;EAC7B,CAAC,EAAE,CAACe,mBAAmB,CAAC,CAAC;EAEzB,MAAMuC,iBAAiB,GAAG,MAAAA,CAAOtD,MAAM,EAAEuD,SAAS,KAAK;IACrD,MAAMC,QAAQ,GAAGlH,eAAe,CAACiH,SAAS,CAAC;IAC3C,IAAI,CAACC,QAAQ,IAAI,CAACA,QAAQ,CAACvC,IAAI,CAAC,CAAC,EAAE;MACjCjI,KAAK,CAACgG,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAIwE,QAAQ,CAAC5E,MAAM,GAAG,GAAG,EAAE;MACzB5F,KAAK,CAACgG,KAAK,CAAC,sCAAsC,CAAC;MACnD;IACF;IAEA,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMtE,WAAW,CAAC0K,SAAS,EAAEC,QAAQ,CAACvC,IAAI,CAAC,CAAC,CAAC;MAC9D,IAAI9D,QAAQ,CAACC,OAAO,EAAE;QACpB;QACAnB,eAAe,CAAC0C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAGrB,IAAI,CAACqB,MAAM,CAAC,CAACzC,GAAG,CAAC4C,OAAO,IAChCA,OAAO,CAACpD,EAAE,KAAKwG,SAAS,GACpB;YAAE,GAAGpD,OAAO;YAAEC,IAAI,EAAEoD,QAAQ,CAACvC,IAAI,CAAC;UAAE,CAAC,GACrCd,OACN;QACF,CAAC,CAAC,CAAC;QAEH9D,iBAAiB,CAACsC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAAC4E,SAAS,GAAG;QAAM,CAAC,CAAC,CAAC;QAC5DhH,kBAAkB,CAACoC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAAC4E,SAAS,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1DvK,KAAK,CAACoE,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACLpE,KAAK,CAACgG,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChG,KAAK,CAACgG,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAMyE,mBAAmB,GAAG,MAAAA,CAAOzD,MAAM,EAAEuD,SAAS,KAAK;IACvD,IAAI,CAAC9D,MAAM,CAACiE,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMvG,QAAQ,GAAG,MAAMrE,aAAa,CAACyK,SAAS,CAAC;MAC/C,IAAIpG,QAAQ,CAACC,OAAO,EAAE;QACpB;QACArC,QAAQ,CAACD,KAAK,CAACyC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACT,EAAE,KAAKiD,MAAM,GACd;UAAE,GAAGxC,IAAI;UAAEgB,aAAa,EAAEhB,IAAI,CAACgB,aAAa,GAAG;QAAE,CAAC,GAClDhB,IACN,CAAC,CAAC;;QAEF;QACAvB,eAAe,CAAC0C,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACqB,MAAM,GAAGrB,IAAI,CAACqB,MAAM,CAAC,CAAC2D,MAAM,CAACxD,OAAO,IAAIA,OAAO,CAACpD,EAAE,KAAKwG,SAAS;QACnE,CAAC,CAAC,CAAC;QAEHvK,KAAK,CAACoE,OAAO,CAAC,8BAA8B,CAAC;MAC/C,CAAC,MAAM;QACLpE,KAAK,CAACgG,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/ChG,KAAK,CAACgG,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAM4E,uBAAuB,GAAGxL,WAAW,CAAC,CAACmL,SAAS,EAAEN,KAAK,KAAK;IAChE1G,kBAAkB,CAACoC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC4E,SAAS,GAAGN;IAAM,CAAC,CAAC,CAAC;EAC/D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,wBAAwB,GAAGzL,WAAW,CAAC,CAAC+K,CAAC,EAAEnD,MAAM,EAAEuD,SAAS,KAAK;IACrE,IAAIJ,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBE,iBAAiB,CAACtD,MAAM,EAAEuD,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,CAACD,iBAAiB,CAAC,CAAC;EAEvB,MAAMQ,cAAc,GAAItG,IAAI,IAAK;IAC/B,IAAI,CAAC1B,YAAY,CAAC0B,IAAI,CAACT,EAAE,CAAC,EAAE,OAAO,IAAI;IAEvC,MAAMmD,QAAQ,GAAGlE,YAAY,CAACwB,IAAI,CAACT,EAAE,CAAC,IAAI,EAAE;IAC5C,MAAMgH,SAAS,GAAG7H,eAAe,CAACsB,IAAI,CAACT,EAAE,CAAC;IAE1C,oBACE7D,OAAA;MAAKiB,SAAS,EAAC,sBAAsB;MAAAE,QAAA,gBACnCnB,OAAA;QAAIiB,SAAS,EAAC,MAAM;QAAAE,QAAA,GAAC,YAAU,EAACmD,IAAI,CAACgB,aAAa,IAAI,CAAC,EAAC,GAAC;MAAA;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAG9DxB,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BnB,OAAA;UAAKkJ,GAAG,EAAE,CAAA1G,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmD,eAAe,KAAItG,cAAe;UAAC4B,SAAS,EAAC,qBAAqB;UAACkI,GAAG,EAAC,SAAS;UAACjI,KAAK,EAAE;YAAC4H,KAAK,EAAE,MAAM;YAAEM,MAAM,EAAE;UAAM;QAAE;UAAA/H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClJxB,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BnB,OAAA;YACEgF,IAAI,EAAC,MAAM;YACX/D,SAAS,EAAC,cAAc;YACxB6J,WAAW,EAAC,oBAAoB;YAChCf,KAAK,EAAErH,UAAU,CAAC4B,IAAI,CAACT,EAAE,CAAC,IAAI,EAAG;YACjCkH,QAAQ,EAAGd,CAAC,IAAKH,mBAAmB,CAACxF,IAAI,CAACT,EAAE,EAAEoG,CAAC,CAACe,MAAM,CAACjB,KAAK,CAAE;YAC9DkB,SAAS,EAAGhB,CAAC,IAAKD,oBAAoB,CAACC,CAAC,EAAE3F,IAAI,CAACT,EAAE,CAAE;YACnDqH,SAAS,EAAE;UAAI;YAAA7J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFxB,OAAA;YAAKiB,SAAS,EAAC,iCAAiC;YAAAE,QAAA,eAC9CnB,OAAA;cAAOiB,SAAS,EAAE,CAACyB,UAAU,CAAC4B,IAAI,CAACT,EAAE,CAAC,IAAI,EAAE,EAAE6B,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;cAAAvE,QAAA,GACxF,CAACuB,UAAU,CAAC4B,IAAI,CAACT,EAAE,CAAC,IAAI,EAAE,EAAE6B,MAAM,EAAC,iBACtC;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNxB,OAAA;UACEiB,SAAS,EAAC,oCAAoC;UAC9CR,OAAO,EAAEA,CAAA,KAAM0J,wBAAwB,CAAC7F,IAAI,CAACT,EAAE,CAAE;UACjDsH,QAAQ,EAAE,CAACzI,UAAU,CAAC4B,IAAI,CAACT,EAAE,CAAC,IAAI,CAACnB,UAAU,CAAC4B,IAAI,CAACT,EAAE,CAAC,CAACkE,IAAI,CAAC,CAAE;UAAA5G,QAAA,eAE9DnB,OAAA,CAACZ,IAAI;YAACmB,IAAI,EAAC;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLqJ,SAAS,gBACR7K,OAAA;QAAKiB,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BnB,OAAA;UAAKiB,SAAS,EAAC,kCAAkC;UAACmK,IAAI,EAAC,QAAQ;UAAAjK,QAAA,eAC7DnB,OAAA;YAAMiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNxB,OAAA;UAAGiB,SAAS,EAAC,uBAAuB;UAAAE,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,gBAENxB,OAAA,CAAAE,SAAA;QAAAiB,QAAA,eAEEnB,OAAA;UAAK6D,EAAE,EAAE,sBAAsBS,IAAI,CAACT,EAAE,EAAG;UAAA1C,QAAA,EAEtC6F,QAAQ,CAAC3C,GAAG,CAAC4C,OAAO,iBACnBjH,OAAA;YAAsBiB,SAAS,EAAC,aAAa;YAAAE,QAAA,gBAC3CnB,OAAA;cAAKkJ,GAAG,EAAEjC,OAAO,CAACvC,MAAO;cAACzD,SAAS,EAAC,qBAAqB;cAACkI,GAAG,EAAElC,OAAO,CAAC1C,IAAK;cAACrD,KAAK,EAAE;gBAAC4H,KAAK,EAAE,MAAM;gBAAEM,MAAM,EAAE;cAAM;YAAE;cAAA/H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvHxB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAAAE,QAAA,gBAC/CnB,OAAA;gBAAKiB,SAAS,EAAC,kDAAkD;gBAAAE,QAAA,gBAC/DnB,OAAA;kBAAKiB,SAAS,EAAC,SAAS;kBAAAE,QAAA,EAAE8F,OAAO,CAAC1C;gBAAI;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAE5CyF,OAAO,CAACzD,OAAO,KAAKA,OAAO,iBAC1BxD,OAAA;kBAAKiB,SAAS,EAAC,cAAc;kBAAAE,QAAA,EAC1B+B,cAAc,CAAC+D,OAAO,CAACpD,EAAE,CAAC,gBACzB7D,OAAA,CAAAE,SAAA;oBAAAiB,QAAA,gBACEnB,OAAA;sBACEiB,SAAS,EAAC,wBAAwB;sBAClCR,OAAO,EAAEA,CAAA,KAAM2J,iBAAiB,CAAC9F,IAAI,CAACT,EAAE,EAAEoD,OAAO,CAACpD,EAAE,CAAE;sBAAA1C,QAAA,eAEtDnB,OAAA,CAACZ,IAAI;wBAACmB,IAAI,EAAC,WAAW;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACTxB,OAAA;sBACEiB,SAAS,EAAC,0BAA0B;sBACpCR,OAAO,EAAEA,CAAA,KAAM;wBACb0C,iBAAiB,CAACsC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACwB,OAAO,CAACpD,EAAE,GAAG;wBAAM,CAAC,CAAC,CAAC;wBAC7DR,kBAAkB,CAACoC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACwB,OAAO,CAACpD,EAAE,GAAG;wBAAG,CAAC,CAAC,CAAC;sBAC7D,CAAE;sBAAA1C,QAAA,eAEFnB,OAAA,CAACZ,IAAI;wBAACmB,IAAI,EAAC,WAAW;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA,eACT,CAAC,gBAEHxB,OAAA,CAAAE,SAAA;oBAAAiB,QAAA,gBACEnB,OAAA;sBACEiB,SAAS,EAAC,gCAAgC;sBAC1CR,OAAO,EAAEA,CAAA,KAAM;wBACb0C,iBAAiB,CAACsC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACwB,OAAO,CAACpD,EAAE,GAAG;wBAAK,CAAC,CAAC,CAAC;wBAC5DR,kBAAkB,CAACoC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE,CAACwB,OAAO,CAACpD,EAAE,GAAGoD,OAAO,CAACC;wBAAK,CAAC,CAAC,CAAC;sBACvE,CAAE;sBACFsB,KAAK,EAAC,cAAc;sBAAArH,QAAA,eAEpBnB,OAAA,CAACZ,IAAI;wBAACmB,IAAI,EAAC,YAAY;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACTxB,OAAA;sBACEiB,SAAS,EAAC,+BAA+B;sBACzCR,OAAO,EAAEA,CAAA,KAAM8J,mBAAmB,CAACjG,IAAI,CAACT,EAAE,EAAEoD,OAAO,CAACpD,EAAE,CAAE;sBACxD2E,KAAK,EAAC,gBAAgB;sBAAArH,QAAA,eAEtBnB,OAAA,CAACZ,IAAI;wBAACmB,IAAI,EAAC,YAAY;wBAACW,KAAK,EAAE;0BAACE,QAAQ,EAAE;wBAAQ;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAEL0B,cAAc,CAAC+D,OAAO,CAACpD,EAAE,CAAC,gBACzB7D,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,gBACnBnB,OAAA;kBACEgF,IAAI,EAAC,MAAM;kBACX/D,SAAS,EAAC,8BAA8B;kBACxC8I,KAAK,EAAE3G,eAAe,CAAC6D,OAAO,CAACpD,EAAE,CAAC,IAAI,EAAG;kBACzCkH,QAAQ,EAAGd,CAAC,IAAKS,uBAAuB,CAACzD,OAAO,CAACpD,EAAE,EAAEoG,CAAC,CAACe,MAAM,CAACjB,KAAK,CAAE;kBACrEkB,SAAS,EAAGhB,CAAC,IAAKU,wBAAwB,CAACV,CAAC,EAAE3F,IAAI,CAACT,EAAE,EAAEoD,OAAO,CAACpD,EAAE,CAAE;kBACnEqH,SAAS,EAAE,GAAI;kBACfG,SAAS;gBAAA;kBAAAhK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFxB,OAAA;kBAAKiB,SAAS,EAAC,iCAAiC;kBAAAE,QAAA,eAC9CnB,OAAA;oBAAOiB,SAAS,EAAE,CAACmC,eAAe,CAAC6D,OAAO,CAACpD,EAAE,CAAC,IAAI,EAAE,EAAE6B,MAAM,GAAG,GAAG,GAAG,cAAc,GAAG,YAAa;oBAAAvE,QAAA,GAChG,CAACiC,eAAe,CAAC6D,OAAO,CAACpD,EAAE,CAAC,IAAI,EAAE,EAAE6B,MAAM,EAAC,iBAC9C;kBAAA;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAENxB,OAAA;gBAAKkB,KAAK,EAAE;kBACVoK,QAAQ,EAAE,YAAY;kBACtBC,SAAS,EAAE,YAAY;kBACvBC,YAAY,EAAE,YAAY;kBAC1BC,UAAU,EAAE,UAAU;kBACtBC,QAAQ,EAAE;gBACZ,CAAE;gBAAAvK,QAAA,EACC8F,OAAO,CAACC;cAAI;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN,eAEDxB,OAAA;gBAAKiB,SAAS,EAAC,uBAAuB;gBAAAE,QAAA,EAAE8F,OAAO,CAACE;cAAS;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA,GAjFEyF,OAAO,CAACpD,EAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkFf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGC;MAAC,gBACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAID,oBACExB,OAAA;IAAKiB,SAAS,EAAC,gBAAgB;IAAAE,QAAA,eAC7BnB,OAAA;MAAKiB,SAAS,EAAC,4BAA4B;MAAAE,QAAA,eACzCnB,OAAA;QAAKiB,SAAS,EAAC,UAAU;QAAAE,QAAA,gBAEbnB,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eACzCnB,OAAA;YAAKiB,SAAS,EAAC,sBAAsB;YAAAE,QAAA,eACnCnB,OAAA;cAAGiB,SAAS,EAAC,uBAAuB;cAAAE,QAAA,EAAC;YAGrC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA;UAAKiB,SAAS,EAAC,wDAAwD;UAAAE,QAAA,gBACrEnB,OAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACXxB,OAAA;YAAKiB,SAAS,EAAC,2BAA2B;YAAAE,QAAA,gBACxCnB,OAAA;cAAKiB,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5BnB,OAAA;gBAAIiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCxB,OAAA;gBAAOiB,SAAS,EAAC,YAAY;gBAAAE,QAAA,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNxB,OAAA;cACEkJ,GAAG,EAAE,CAAA1G,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmD,eAAe,KAAItG,cAAe;cACpD4B,SAAS,EAAC,gBAAgB;cAC1BkI,GAAG,EAAE,CAAA3G,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgC,IAAI,KAAI,SAAU;cACpCtD,KAAK,EAAE;gBAAC4H,KAAK,EAAE,MAAM;gBAAEM,MAAM,EAAE;cAAM;YAAE;cAAA/H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA,CAACV,QAAQ;UAACqM,YAAY,EAAE1D,gBAAiB;UAACzF,WAAW,EAAEA;QAAY;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAIrEc,cAAc,iBACbtC,OAAA;UAAKiB,SAAS,EAAC,WAAW;UAACC,KAAK,EAAE;YAChC0K,SAAS,EAAE,yBAAyB;YACpCC,MAAM,EAAE,oBAAoB;YAC5BnF,eAAe,EAAE;UACnB,CAAE;UAAAvF,QAAA,eACAnB,OAAA;YAAKiB,SAAS,EAAC,4BAA4B;YAAAE,QAAA,gBACzCnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAACmK,IAAI,EAAC,QAAQ;cAAAjK,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNxB,OAAA;cAAIiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DxB,OAAA;cAAGiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAM,OAAO,gBACN9B,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA;YAAKiB,SAAS,EAAC,gBAAgB;YAACmK,IAAI,EAAC,QAAQ;YAAAjK,QAAA,eAC3CnB,OAAA;cAAMiB,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNxB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAAqB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,GACJI,KAAK,CAAC8D,MAAM,KAAK,CAAC,gBACpB1F,OAAA;UAAKiB,SAAS,EAAC,kBAAkB;UAAAE,QAAA,gBAC/BnB,OAAA,CAACZ,IAAI;YAACmB,IAAI,EAAC,kBAAkB;YAACW,KAAK,EAAE;cAAEE,QAAQ,EAAE,MAAM;cAAE0K,KAAK,EAAE;YAAU;UAAE;YAAAzK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ExB,OAAA;YAAGiB,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAA8C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,gBAENxB,OAAA,CAAAE,SAAA;UAAAiB,QAAA,GAEGS,KAAK,CAACyC,GAAG,CAAEC,IAAI,iBAChBtE,OAAA;YAAmBiB,SAAS,EAAC,WAAW;YAAAE,QAAA,eACtCnB,OAAA;cAAKiB,SAAS,EAAC,WAAW;cAAAE,QAAA,gBAExBnB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA;kBAAKkJ,GAAG,EAAE5E,IAAI,CAACC,IAAI,CAACG,MAAO;kBAACzD,SAAS,EAAC,qBAAqB;kBAACkI,GAAG,EAAE7E,IAAI,CAACC,IAAI,CAACC,IAAK;kBAACtD,KAAK,EAAE;oBAAC4H,KAAK,EAAE,MAAM;oBAAEM,MAAM,EAAE;kBAAM;gBAAE;kBAAA/H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3HxB,OAAA;kBAAKiB,SAAS,EAAC,aAAa;kBAAAE,QAAA,gBAC1BnB,OAAA;oBAAIiB,SAAS,EAAC,MAAM;oBAAAE,QAAA,EAAEmD,IAAI,CAACC,IAAI,CAACC;kBAAI;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1CxB,OAAA;oBAAOiB,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAE,IAAIiG,IAAI,CAAC9C,IAAI,CAACkB,UAAU,CAAC,CAAC8B,kBAAkB,CAAC;kBAAC;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,MAAM;gBAAAE,QAAA,GAClBqI,iBAAiB,CAAClF,IAAI,CAACM,OAAO,EAAEN,IAAI,CAAC,EACrCuE,WAAW,CAACvE,IAAI,CAACQ,KAAK,CAAC;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eAGNxB,OAAA;gBAAKiB,SAAS,EAAC,gCAAgC;gBAAAE,QAAA,gBAC7CnB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAE+D,IAAI,CAAC5D,OAAO,GAAG,WAAW,GAAG,mBAAoB;kBACvDF,KAAK,EAAE8D,IAAI,CAACc,KAAM;kBAClB3E,OAAO,EAAEA,CAAA,KAAMiH,UAAU,CAACpD,IAAI,CAACT,EAAE,CAAE;kBACnCnD,OAAO,EAAE4D,IAAI,CAAC5D,OAAQ;kBACtBE,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAE8D,IAAI,CAACgB,aAAa,IAAI,CAAE;kBAC/B7E,OAAO,EAAEA,CAAA,KAAMkH,aAAa,CAACrD,IAAI,CAACT,EAAE,CAAE;kBACtCjD,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFxB,OAAA,CAACG,YAAY;kBACXI,IAAI,EAAC,2BAA2B;kBAChCE,OAAO,EAAEA,CAAA,KAAM4H,WAAW,CAAC/D,IAAI,CAAE;kBACjC3D,MAAM,EAAE,IAAK;kBACbC,WAAW,EAAEA,WAAY;kBACzBC,iBAAiB,EAAEA;gBAAkB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLoJ,cAAc,CAACtG,IAAI,CAAC;YAAA;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC,GA7CE8C,IAAI,CAACT,EAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CZ,CACJ,CAAC,EAGDQ,WAAW,iBACVhC,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,gBAC/BnB,OAAA;cAAKiB,SAAS,EAAC,kCAAkC;cAACmK,IAAI,EAAC,QAAQ;cAAAjK,QAAA,eAC7DnB,OAAA;gBAAMiB,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACNxB,OAAA;cAAGiB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CACN,EAEA,CAACY,OAAO,IAAIR,KAAK,CAAC8D,MAAM,GAAG,CAAC,iBAC3B1F,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAAE,QAAA,eAC/BnB,OAAA;cAAGiB,SAAS,EAAC,YAAY;cAAAE,QAAA,EAAC;YAAqC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,GAAA,CA7uBID,MAAM;AAAAqK,GAAA,GAANrK,MAAM;AA+uBZ,eAAeA,MAAM;AAAC,IAAApB,EAAA,EAAAmB,GAAA,EAAAsK,GAAA;AAAAC,YAAA,CAAA1L,EAAA;AAAA0L,YAAA,CAAAvK,GAAA;AAAAuK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}