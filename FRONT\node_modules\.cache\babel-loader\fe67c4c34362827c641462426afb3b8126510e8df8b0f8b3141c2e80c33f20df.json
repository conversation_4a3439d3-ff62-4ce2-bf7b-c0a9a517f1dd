{"ast": null, "code": "import axios from 'axios';\n\n// Get the current hostname\nconst hostname = window.location.hostname;\n\n// Determine the base URL based on environment\nconst BASE_URL = process.env.REACT_APP_API_URL || (hostname === 'localhost' ? 'http://localhost:4000' : 'https://api.nxgenvarsity.com');\nconsole.log('API Base URL:', BASE_URL);\nconsole.log(\"BASE_URL frontend:\", BASE_URL);\nconst api = axios.create({\n  baseURL: BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor with better error logging\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = token;\n  }\n  console.log('Making request to:', config.baseURL + config.url, {\n    method: config.method,\n    headers: config.headers\n  });\n  return config;\n}, error => {\n  console.error('Request interceptor error:', error);\n  return Promise.reject(error);\n});\n\n// Add response interceptor with better error handling\napi.interceptors.response.use(response => {\n  console.log('Response received:', {\n    url: response.config.url,\n    status: response.status,\n    data: response.data\n  });\n  return response;\n}, error => {\n  if (error.code === 'ECONNABORTED') {\n    console.error('Request timeout:', {\n      url: error.config.url,\n      method: error.config.method,\n      timeout: error.config.timeout\n    });\n  } else if (!error.response) {\n    var _error$config;\n    console.error('Network error:', {\n      url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n      message: 'No response received from server',\n      error: error.message\n    });\n  } else {\n    console.error('API error:', {\n      url: error.config.url,\n      status: error.response.status,\n      data: error.response.data\n    });\n  }\n  return Promise.reject(error);\n});\nclass ApiController {\n  static async get(endpoint, params = {}) {\n    try {\n      const response = await api.get(endpoint, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async post(endpoint, data = {}) {\n    console.log(\"POST request to:\", endpoint);\n    console.log(\"Data being sent:\", data);\n    try {\n      const response = await api.post(endpoint, data);\n      console.log(\"POST request response:\", response);\n      return response.data;\n    } catch (error) {\n      var _error$response$data;\n      // Don't log expected errors like 404 (post not found)\n      if (!(error.response && error.response.status === 404 && ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error_msg) === 'Post not found')) {\n        console.error(\"POST request error:\", error);\n      }\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async put(endpoint, data = {}) {\n    try {\n      const response = await api.put(endpoint, data);\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async patch(endpoint, data = {}) {\n    try {\n      const response = await api.patch(endpoint, data);\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async delete(endpoint) {\n    try {\n      const response = await api.delete(endpoint);\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async uploadFile(endpoint, formData, additionalData = {}, onProgress = null) {\n    try {\n      console.log('UPLOAD_FILE called with endpointd:', endpoint);\n      console.log('Base URL:', api.defaults.baseURL);\n      console.log('Full URL will be:', api.defaults.baseURL + endpoint);\n\n      // Add any additional data to the existing FormData\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n      console.log('FormData contents before request:');\n      for (let pair of formData.entries()) {\n        if (pair[1] instanceof File) {\n          console.log(`${pair[0]}: File(${pair[1].name}, ${pair[1].size} bytes, ${pair[1].type})`);\n        } else {\n          console.log(`${pair[0]}: ${pair[1]}`);\n        }\n      }\n      const response = await api.post(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: onProgress ? progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          onProgress(percentCompleted);\n        } : undefined\n      });\n      console.log('Upload successful, response:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Upload failed with error:', error);\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async uploadFileWithMethod(endpoint, formData, method = 'POST', additionalData = {}, onProgress = null) {\n    try {\n      // Add any additional data to the existing FormData\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n\n      // Choose the appropriate axios method\n      const axiosMethod = method.toLowerCase() === 'put' ? api.put : method.toLowerCase() === 'patch' ? api.patch : api.post;\n      const response = await axiosMethod(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: onProgress ? progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          onProgress(percentCompleted);\n        } : undefined\n      });\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static async uploadMultipleFiles(endpoint, files, additionalData = {}, onProgress = null) {\n    try {\n      const formData = new FormData();\n      files.forEach((file, index) => {\n        formData.append(`files[${index}]`, file);\n      });\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n      const response = await api.post(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: onProgress ? progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          onProgress(percentCompleted);\n        } : undefined\n      });\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n  static handleError(error) {\n    var _error$response$data2, _error$response$data3;\n    // Don't log expected errors like 404 (post not found) or course not found to reduce console noise\n    if (error.response && error.response.status === 404 && (((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.error_msg) === 'Post not found' || ((_error$response$data3 = error.response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) === 'Course not found or inactive')) {\n      // This is an expected error, don't log it\n      return;\n    }\n    if (error.response) {\n      console.error('Response Error:', error.response.data);\n      console.error('Status:', error.response.status);\n      console.error('Headers:', error.response.headers);\n    } else if (error.request) {\n      console.error('Request Error:', error.request);\n    } else {\n      console.error('Error:', error.message);\n    }\n  }\n}\nexport const GET = ApiController.get;\nexport const POST = ApiController.post;\nexport const PUT = ApiController.put;\nexport const PATCH = ApiController.patch;\nexport const DELETE = ApiController.delete;\nexport const UPLOAD_FILE = ApiController.uploadFile;\nexport const UPLOAD_FILE_WITH_METHOD = ApiController.uploadFileWithMethod;\nexport const UPLOAD_MULTIPLE_FILES = ApiController.uploadMultipleFiles;\nexport default ApiController;", "map": {"version": 3, "names": ["axios", "hostname", "window", "location", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "url", "method", "error", "Promise", "reject", "response", "status", "data", "code", "_error$config", "message", "ApiController", "get", "endpoint", "params", "handleError", "post", "_error$response$data", "error_msg", "put", "patch", "delete", "uploadFile", "formData", "additionalData", "onProgress", "defaults", "Object", "keys", "for<PERSON>ach", "key", "append", "pair", "entries", "File", "name", "size", "type", "onUploadProgress", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "undefined", "uploadFileWithMethod", "axiosMethod", "toLowerCase", "uploadMultipleFiles", "files", "FormData", "file", "index", "_error$response$data2", "_error$response$data3", "GET", "POST", "PUT", "PATCH", "DELETE", "UPLOAD_FILE", "UPLOAD_FILE_WITH_METHOD", "UPLOAD_MULTIPLE_FILES"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/services/apiController.js"], "sourcesContent": ["import axios from 'axios';\n\n// Get the current hostname\nconst hostname = window.location.hostname;\n\n// Determine the base URL based on environment\nconst BASE_URL = process.env.REACT_APP_API_URL || \n  (hostname === 'localhost' ? 'http://localhost:4000' : 'https://api.nxgenvarsity.com');\n\nconsole.log('API Base URL:', BASE_URL);\n\n\nconsole.log(\"BASE_URL frontend:\",BASE_URL);\nconst api = axios.create({\n  baseURL: BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptor with better error logging\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = token;\n    }\n    console.log('Making request to:', config.baseURL + config.url, {\n      method: config.method,\n      headers: config.headers\n    });\n    return config;\n  },\n  (error) => {\n    console.error('Request interceptor error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor with better error handling\napi.interceptors.response.use(\n  (response) => {\n    console.log('Response received:', {\n      url: response.config.url,\n      status: response.status,\n      data: response.data\n    });\n    return response;\n  },\n  (error) => {\n    if (error.code === 'ECONNABORTED') {\n      console.error('Request timeout:', {\n        url: error.config.url,\n        method: error.config.method,\n        timeout: error.config.timeout\n      });\n    } else if (!error.response) {\n      console.error('Network error:', {\n        url: error.config?.url,\n        message: 'No response received from server',\n        error: error.message\n      });\n    } else {\n      console.error('API error:', {\n        url: error.config.url,\n        status: error.response.status,\n        data: error.response.data\n      });\n    }\n    return Promise.reject(error);\n  }\n);\n\nclass ApiController {\n  static async get(endpoint, params = {}) {\n    try {\n      const response = await api.get(endpoint, { params });\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async post(endpoint, data = {}) {\n    console.log(\"POST request to:\",endpoint);\n    console.log(\"Data being sent:\", data);\n    try {\n      const response = await api.post(endpoint, data);\n      console.log(\"POST request response:\", response);\n      return response.data;\n    } catch (error) {\n      // Don't log expected errors like 404 (post not found)\n      if (!(error.response && error.response.status === 404 && error.response.data?.error_msg === 'Post not found')) {\n        console.error(\"POST request error:\", error);\n      }\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async put(endpoint, data = {}) {\n    try {\n      const response = await api.put(endpoint, data);\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async patch(endpoint, data = {}) {\n    try {\n      const response = await api.patch(endpoint, data);\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async delete(endpoint) {\n    try {\n      const response = await api.delete(endpoint);\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async uploadFile(endpoint, formData, additionalData = {}, onProgress = null) {\n    try {\n      console.log('UPLOAD_FILE called with endpointd:',endpoint);\n      console.log('Base URL:', api.defaults.baseURL);\n      console.log('Full URL will be:', api.defaults.baseURL + endpoint);\n\n      // Add any additional data to the existing FormData\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n\n      console.log('FormData contents before request:');\n      for (let pair of formData.entries()) {\n        if (pair[1] instanceof File) {\n          console.log(`${pair[0]}: File(${pair[1].name}, ${pair[1].size} bytes, ${pair[1].type})`);\n        } else {\n          console.log(`${pair[0]}: ${pair[1]}`);\n        }\n      }\n\n      const response = await api.post(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        onUploadProgress: onProgress\n          ? (progressEvent) => {\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n              onProgress(percentCompleted);\n            }\n          : undefined,\n      });\n\n      console.log('Upload successful, response:', response);\n      return response.data;\n    } catch (error) {\n      console.error('Upload failed with error:', error);\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async uploadFileWithMethod(endpoint, formData, method = 'POST', additionalData = {}, onProgress = null) {\n    try {\n      // Add any additional data to the existing FormData\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n\n      // Choose the appropriate axios method\n      const axiosMethod = method.toLowerCase() === 'put' ? api.put :\n                         method.toLowerCase() === 'patch' ? api.patch :\n                         api.post;\n\n      const response = await axiosMethod(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        onUploadProgress: onProgress\n          ? (progressEvent) => {\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n              onProgress(percentCompleted);\n            }\n          : undefined,\n      });\n\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static async uploadMultipleFiles(endpoint, files, additionalData = {}, onProgress = null) {\n    try {\n      const formData = new FormData();\n\n      files.forEach((file, index) => {\n        formData.append(`files[${index}]`, file);\n      });\n\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n\n      const response = await api.post(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        onUploadProgress: onProgress\n          ? (progressEvent) => {\n              const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n              onProgress(percentCompleted);\n            }\n          : undefined,\n      });\n\n      return response.data;\n    } catch (error) {\n      ApiController.handleError(error);\n      throw error;\n    }\n  }\n\n  static handleError(error) {\n    // Don't log expected errors like 404 (post not found) or course not found to reduce console noise\n    if (error.response && error.response.status === 404 && \n        (error.response.data?.error_msg === 'Post not found' || \n         error.response.data?.message === 'Course not found or inactive')) {\n      // This is an expected error, don't log it\n      return;\n    }\n    \n    if (error.response) {\n      console.error('Response Error:', error.response.data);\n      console.error('Status:', error.response.status);\n      console.error('Headers:', error.response.headers);\n    } else if (error.request) {\n      console.error('Request Error:', error.request);\n    } else {\n      console.error('Error:', error.message);\n    }\n  }\n}\n\nexport const GET = ApiController.get;\nexport const POST = ApiController.post;\nexport const PUT = ApiController.put;\nexport const PATCH = ApiController.patch;\nexport const DELETE = ApiController.delete;\nexport const UPLOAD_FILE = ApiController.uploadFile;\nexport const UPLOAD_FILE_WITH_METHOD = ApiController.uploadFileWithMethod;\nexport const UPLOAD_MULTIPLE_FILES = ApiController.uploadMultipleFiles;\n\nexport default ApiController;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ;;AAEzC;AACA,MAAMG,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,KAC3CN,QAAQ,KAAK,WAAW,GAAG,uBAAuB,GAAG,8BAA8B,CAAC;AAEvFO,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEL,QAAQ,CAAC;AAGtCI,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAACL,QAAQ,CAAC;AAC1C,MAAMM,GAAG,GAAGV,KAAK,CAACW,MAAM,CAAC;EACvBC,OAAO,EAAER,QAAQ;EACjBS,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAGH,KAAK;EACtC;EACAX,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAES,MAAM,CAACN,OAAO,GAAGM,MAAM,CAACK,GAAG,EAAE;IAC7DC,MAAM,EAAEN,MAAM,CAACM,MAAM;IACrBV,OAAO,EAAEI,MAAM,CAACJ;EAClB,CAAC,CAAC;EACF,OAAOI,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACTjB,OAAO,CAACiB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;EAClD,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAf,GAAG,CAACK,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,IAAK;EACZpB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;IAChCc,GAAG,EAAEK,QAAQ,CAACV,MAAM,CAACK,GAAG;IACxBM,MAAM,EAAED,QAAQ,CAACC,MAAM;IACvBC,IAAI,EAAEF,QAAQ,CAACE;EACjB,CAAC,CAAC;EACF,OAAOF,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EACT,IAAIA,KAAK,CAACM,IAAI,KAAK,cAAc,EAAE;IACjCvB,OAAO,CAACiB,KAAK,CAAC,kBAAkB,EAAE;MAChCF,GAAG,EAAEE,KAAK,CAACP,MAAM,CAACK,GAAG;MACrBC,MAAM,EAAEC,KAAK,CAACP,MAAM,CAACM,MAAM;MAC3BX,OAAO,EAAEY,KAAK,CAACP,MAAM,CAACL;IACxB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI,CAACY,KAAK,CAACG,QAAQ,EAAE;IAAA,IAAAI,aAAA;IAC1BxB,OAAO,CAACiB,KAAK,CAAC,gBAAgB,EAAE;MAC9BF,GAAG,GAAAS,aAAA,GAAEP,KAAK,CAACP,MAAM,cAAAc,aAAA,uBAAZA,aAAA,CAAcT,GAAG;MACtBU,OAAO,EAAE,kCAAkC;MAC3CR,KAAK,EAAEA,KAAK,CAACQ;IACf,CAAC,CAAC;EACJ,CAAC,MAAM;IACLzB,OAAO,CAACiB,KAAK,CAAC,YAAY,EAAE;MAC1BF,GAAG,EAAEE,KAAK,CAACP,MAAM,CAACK,GAAG;MACrBM,MAAM,EAAEJ,KAAK,CAACG,QAAQ,CAACC,MAAM;MAC7BC,IAAI,EAAEL,KAAK,CAACG,QAAQ,CAACE;IACvB,CAAC,CAAC;EACJ;EACA,OAAOJ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMS,aAAa,CAAC;EAClB,aAAaC,GAAGA,CAACC,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMlB,GAAG,CAACyB,GAAG,CAACC,QAAQ,EAAE;QAAEC;MAAO,CAAC,CAAC;MACpD,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAac,IAAIA,CAACH,QAAQ,EAAEN,IAAI,GAAG,CAAC,CAAC,EAAE;IACrCtB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAC2B,QAAQ,CAAC;IACxC5B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEqB,IAAI,CAAC;IACrC,IAAI;MACF,MAAMF,QAAQ,GAAG,MAAMlB,GAAG,CAAC6B,IAAI,CAACH,QAAQ,EAAEN,IAAI,CAAC;MAC/CtB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,QAAQ,CAAC;MAC/C,OAAOA,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MAAA,IAAAe,oBAAA;MACd;MACA,IAAI,EAAEf,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAI,EAAAW,oBAAA,GAAAf,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAAU,oBAAA,uBAAnBA,oBAAA,CAAqBC,SAAS,MAAK,gBAAgB,CAAC,EAAE;QAC7GjC,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;MACAS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAaiB,GAAGA,CAACN,QAAQ,EAAEN,IAAI,GAAG,CAAC,CAAC,EAAE;IACpC,IAAI;MACF,MAAMF,QAAQ,GAAG,MAAMlB,GAAG,CAACgC,GAAG,CAACN,QAAQ,EAAEN,IAAI,CAAC;MAC9C,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAakB,KAAKA,CAACP,QAAQ,EAAEN,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI;MACF,MAAMF,QAAQ,GAAG,MAAMlB,GAAG,CAACiC,KAAK,CAACP,QAAQ,EAAEN,IAAI,CAAC;MAChD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAamB,MAAMA,CAACR,QAAQ,EAAE;IAC5B,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMlB,GAAG,CAACkC,MAAM,CAACR,QAAQ,CAAC;MAC3C,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAaoB,UAAUA,CAACT,QAAQ,EAAEU,QAAQ,EAAEC,cAAc,GAAG,CAAC,CAAC,EAAEC,UAAU,GAAG,IAAI,EAAE;IAClF,IAAI;MACFxC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAC2B,QAAQ,CAAC;MAC1D5B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,GAAG,CAACuC,QAAQ,CAACrC,OAAO,CAAC;MAC9CJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,GAAG,CAACuC,QAAQ,CAACrC,OAAO,GAAGwB,QAAQ,CAAC;;MAEjE;MACAc,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;QACzCP,QAAQ,CAACQ,MAAM,CAACD,GAAG,EAAEN,cAAc,CAACM,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;MAEF7C,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,KAAK,IAAI8C,IAAI,IAAIT,QAAQ,CAACU,OAAO,CAAC,CAAC,EAAE;QACnC,IAAID,IAAI,CAAC,CAAC,CAAC,YAAYE,IAAI,EAAE;UAC3BjD,OAAO,CAACC,GAAG,CAAC,GAAG8C,IAAI,CAAC,CAAC,CAAC,UAAUA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKH,IAAI,CAAC,CAAC,CAAC,CAACI,IAAI,WAAWJ,IAAI,CAAC,CAAC,CAAC,CAACK,IAAI,GAAG,CAAC;QAC1F,CAAC,MAAM;UACLpD,OAAO,CAACC,GAAG,CAAC,GAAG8C,IAAI,CAAC,CAAC,CAAC,KAAKA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC;MACF;MAEA,MAAM3B,QAAQ,GAAG,MAAMlB,GAAG,CAAC6B,IAAI,CAACH,QAAQ,EAAEU,QAAQ,EAAE;QAClDhC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD+C,gBAAgB,EAAEb,UAAU,GACvBc,aAAa,IAAK;UACjB,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAEH,aAAa,CAACI,MAAM,GAAG,GAAG,GAAIJ,aAAa,CAACK,KAAK,CAAC;UACvFnB,UAAU,CAACe,gBAAgB,CAAC;QAC9B,CAAC,GACDK;MACN,CAAC,CAAC;MAEF5D,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmB,QAAQ,CAAC;MACrD,OAAOA,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAa4C,oBAAoBA,CAACjC,QAAQ,EAAEU,QAAQ,EAAEtB,MAAM,GAAG,MAAM,EAAEuB,cAAc,GAAG,CAAC,CAAC,EAAEC,UAAU,GAAG,IAAI,EAAE;IAC7G,IAAI;MACF;MACAE,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;QACzCP,QAAQ,CAACQ,MAAM,CAACD,GAAG,EAAEN,cAAc,CAACM,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;;MAEF;MACA,MAAMiB,WAAW,GAAG9C,MAAM,CAAC+C,WAAW,CAAC,CAAC,KAAK,KAAK,GAAG7D,GAAG,CAACgC,GAAG,GACzClB,MAAM,CAAC+C,WAAW,CAAC,CAAC,KAAK,OAAO,GAAG7D,GAAG,CAACiC,KAAK,GAC5CjC,GAAG,CAAC6B,IAAI;MAE3B,MAAMX,QAAQ,GAAG,MAAM0C,WAAW,CAAClC,QAAQ,EAAEU,QAAQ,EAAE;QACrDhC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD+C,gBAAgB,EAAEb,UAAU,GACvBc,aAAa,IAAK;UACjB,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAEH,aAAa,CAACI,MAAM,GAAG,GAAG,GAAIJ,aAAa,CAACK,KAAK,CAAC;UACvFnB,UAAU,CAACe,gBAAgB,CAAC;QAC9B,CAAC,GACDK;MACN,CAAC,CAAC;MAEF,OAAOxC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,aAAa+C,mBAAmBA,CAACpC,QAAQ,EAAEqC,KAAK,EAAE1B,cAAc,GAAG,CAAC,CAAC,EAAEC,UAAU,GAAG,IAAI,EAAE;IACxF,IAAI;MACF,MAAMF,QAAQ,GAAG,IAAI4B,QAAQ,CAAC,CAAC;MAE/BD,KAAK,CAACrB,OAAO,CAAC,CAACuB,IAAI,EAAEC,KAAK,KAAK;QAC7B9B,QAAQ,CAACQ,MAAM,CAAC,SAASsB,KAAK,GAAG,EAAED,IAAI,CAAC;MAC1C,CAAC,CAAC;MAEFzB,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;QACzCP,QAAQ,CAACQ,MAAM,CAACD,GAAG,EAAEN,cAAc,CAACM,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;MAEF,MAAMzB,QAAQ,GAAG,MAAMlB,GAAG,CAAC6B,IAAI,CAACH,QAAQ,EAAEU,QAAQ,EAAE;QAClDhC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD+C,gBAAgB,EAAEb,UAAU,GACvBc,aAAa,IAAK;UACjB,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAAEH,aAAa,CAACI,MAAM,GAAG,GAAG,GAAIJ,aAAa,CAACK,KAAK,CAAC;UACvFnB,UAAU,CAACe,gBAAgB,CAAC;QAC9B,CAAC,GACDK;MACN,CAAC,CAAC;MAEF,OAAOxC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdS,aAAa,CAACI,WAAW,CAACb,KAAK,CAAC;MAChC,MAAMA,KAAK;IACb;EACF;EAEA,OAAOa,WAAWA,CAACb,KAAK,EAAE;IAAA,IAAAoD,qBAAA,EAAAC,qBAAA;IACxB;IACA,IAAIrD,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,KAC9C,EAAAgD,qBAAA,GAAApD,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAA+C,qBAAA,uBAAnBA,qBAAA,CAAqBpC,SAAS,MAAK,gBAAgB,IACnD,EAAAqC,qBAAA,GAAArD,KAAK,CAACG,QAAQ,CAACE,IAAI,cAAAgD,qBAAA,uBAAnBA,qBAAA,CAAqB7C,OAAO,MAAK,8BAA8B,CAAC,EAAE;MACrE;MACA;IACF;IAEA,IAAIR,KAAK,CAACG,QAAQ,EAAE;MAClBpB,OAAO,CAACiB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACG,QAAQ,CAACE,IAAI,CAAC;MACrDtB,OAAO,CAACiB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACG,QAAQ,CAACC,MAAM,CAAC;MAC/CrB,OAAO,CAACiB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAACG,QAAQ,CAACd,OAAO,CAAC;IACnD,CAAC,MAAM,IAAIW,KAAK,CAACT,OAAO,EAAE;MACxBR,OAAO,CAACiB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACT,OAAO,CAAC;IAChD,CAAC,MAAM;MACLR,OAAO,CAACiB,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACQ,OAAO,CAAC;IACxC;EACF;AACF;AAEA,OAAO,MAAM8C,GAAG,GAAG7C,aAAa,CAACC,GAAG;AACpC,OAAO,MAAM6C,IAAI,GAAG9C,aAAa,CAACK,IAAI;AACtC,OAAO,MAAM0C,GAAG,GAAG/C,aAAa,CAACQ,GAAG;AACpC,OAAO,MAAMwC,KAAK,GAAGhD,aAAa,CAACS,KAAK;AACxC,OAAO,MAAMwC,MAAM,GAAGjD,aAAa,CAACU,MAAM;AAC1C,OAAO,MAAMwC,WAAW,GAAGlD,aAAa,CAACW,UAAU;AACnD,OAAO,MAAMwC,uBAAuB,GAAGnD,aAAa,CAACmC,oBAAoB;AACzE,OAAO,MAAMiB,qBAAqB,GAAGpD,aAAa,CAACsC,mBAAmB;AAEtE,eAAetC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}